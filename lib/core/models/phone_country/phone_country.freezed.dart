// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phone_country.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PhoneCountry _$PhoneCountryFromJson(Map<String, dynamic> json) {
  return _PhoneCountry.fromJson(json);
}

/// @nodoc
mixin _$PhoneCountry {
  String get name => throw _privateConstructorUsedError;
  String get code => throw _privateConstructorUsedError;
  String get phoneCode => throw _privateConstructorUsedError;
  String get flagEmoji => throw _privateConstructorUsedError;

  /// Serializes this PhoneCountry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PhoneCountry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhoneCountryCopyWith<PhoneCountry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneCountryCopyWith<$Res> {
  factory $PhoneCountryCopyWith(
          PhoneCountry value, $Res Function(PhoneCountry) then) =
      _$PhoneCountryCopyWithImpl<$Res, PhoneCountry>;
  @useResult
  $Res call({String name, String code, String phoneCode, String flagEmoji});
}

/// @nodoc
class _$PhoneCountryCopyWithImpl<$Res, $Val extends PhoneCountry>
    implements $PhoneCountryCopyWith<$Res> {
  _$PhoneCountryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneCountry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? code = null,
    Object? phoneCode = null,
    Object? flagEmoji = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      phoneCode: null == phoneCode
          ? _value.phoneCode
          : phoneCode // ignore: cast_nullable_to_non_nullable
              as String,
      flagEmoji: null == flagEmoji
          ? _value.flagEmoji
          : flagEmoji // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PhoneCountryImplCopyWith<$Res>
    implements $PhoneCountryCopyWith<$Res> {
  factory _$$PhoneCountryImplCopyWith(
          _$PhoneCountryImpl value, $Res Function(_$PhoneCountryImpl) then) =
      __$$PhoneCountryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, String code, String phoneCode, String flagEmoji});
}

/// @nodoc
class __$$PhoneCountryImplCopyWithImpl<$Res>
    extends _$PhoneCountryCopyWithImpl<$Res, _$PhoneCountryImpl>
    implements _$$PhoneCountryImplCopyWith<$Res> {
  __$$PhoneCountryImplCopyWithImpl(
      _$PhoneCountryImpl _value, $Res Function(_$PhoneCountryImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneCountry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? code = null,
    Object? phoneCode = null,
    Object? flagEmoji = null,
  }) {
    return _then(_$PhoneCountryImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      phoneCode: null == phoneCode
          ? _value.phoneCode
          : phoneCode // ignore: cast_nullable_to_non_nullable
              as String,
      flagEmoji: null == flagEmoji
          ? _value.flagEmoji
          : flagEmoji // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PhoneCountryImpl implements _PhoneCountry {
  const _$PhoneCountryImpl(
      {required this.name,
      required this.code,
      required this.phoneCode,
      required this.flagEmoji});

  factory _$PhoneCountryImpl.fromJson(Map<String, dynamic> json) =>
      _$$PhoneCountryImplFromJson(json);

  @override
  final String name;
  @override
  final String code;
  @override
  final String phoneCode;
  @override
  final String flagEmoji;

  @override
  String toString() {
    return 'PhoneCountry(name: $name, code: $code, phoneCode: $phoneCode, flagEmoji: $flagEmoji)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneCountryImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.phoneCode, phoneCode) ||
                other.phoneCode == phoneCode) &&
            (identical(other.flagEmoji, flagEmoji) ||
                other.flagEmoji == flagEmoji));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, code, phoneCode, flagEmoji);

  /// Create a copy of PhoneCountry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneCountryImplCopyWith<_$PhoneCountryImpl> get copyWith =>
      __$$PhoneCountryImplCopyWithImpl<_$PhoneCountryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PhoneCountryImplToJson(
      this,
    );
  }
}

abstract class _PhoneCountry implements PhoneCountry {
  const factory _PhoneCountry(
      {required final String name,
      required final String code,
      required final String phoneCode,
      required final String flagEmoji}) = _$PhoneCountryImpl;

  factory _PhoneCountry.fromJson(Map<String, dynamic> json) =
      _$PhoneCountryImpl.fromJson;

  @override
  String get name;
  @override
  String get code;
  @override
  String get phoneCode;
  @override
  String get flagEmoji;

  /// Create a copy of PhoneCountry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneCountryImplCopyWith<_$PhoneCountryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
