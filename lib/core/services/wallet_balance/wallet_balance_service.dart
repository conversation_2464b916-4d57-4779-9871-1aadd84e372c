import 'package:injectable/injectable.dart';
import 'package:sf_app/features/home/<USER>/models/balance/balance_model.dart';

/// A singleton service that manages wallet balance data throughout the app.
///
/// This service provides:
/// - Centralized balance data storage and access
/// - Methods to update and clear balance information
/// - Getters for different balance types (community, cash)
/// 
@singleton
class WalletBalanceService {
  WalletBalanceService._();

  static final WalletBalanceService _instance = WalletBalanceService._();

  factory WalletBalanceService() => _instance;

  /// Cached balance data model
  BalanceModel? _balanceData;
  
  /// Returns the current cached balance data, may be null if not yet initialized
  BalanceModel? get balanceData => _balanceData;

  /// Returns the community balance information, defaults to empty if not available
  CommunityBalance get communityBalance =>
      _balanceData?.data.community ?? const CommunityBalance();

  /// Returns the cash balance as a double, defaults to 0.0 if not available
  double get cashBalance => double.tryParse(_balanceData?.data.cash.toString() ?? '0') ?? 0.0;

  /// Updates the cached balance data with new information from the API
  /// [balanceData] The new balance data to store
  void updateBalance(BalanceModel balanceData) => _balanceData = balanceData;

  /// Clears all cached balance data
  /// Should be called during logout to prevent data leaks between sessions
  void clearBalance() => _balanceData = null;
}