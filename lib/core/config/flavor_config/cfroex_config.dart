import 'dart:ui';

import 'package:sf_app/core/config/app_config.dart';
import 'package:sf_app/flavors.dart';

import '../../constants/enums.dart';

/// Sets up the configuration for the CFROEX flavor of the app
///
/// Takes a [kDebug] parameter to determine if running in debug mode
/// Returns an [AppConfig] instance with CFROEX-specific settings:
/// * Uses CFROEX-specific API endpoints and assets
/// * Enables trading wallet and transfer features
/// * Disables benefit rules and product fields
/// * Uses Spanish (ES) locale
/// * Uses static payment types instead of API ones
/// * Disables mentor vip level
/// * Disables upper case password protection
/// * Enables withdraw history
setupCfroexConfig(bool kDebug) => AppConfig(
      flavor: Flavor.cfroex,
      appName: 'Forex Fusion',
      baseUrl: kDebug
          ? 'https://api.superfuture.world'
          : 'https://api.chalanforex.com',
      marketWsUrl: kDebug
          ? 'wss://api.superfuture.world/ws'
          : 'wss://api.chalanforex.com/ws',
      appUrl: 'https://chalanforex.com/',
      icon: 'assets/logo/cfroex/logo.svg',
      introVideo: 'assets/splash/cfroex/introVideo.mp4',
      showDebugVersionTag: kDebug,
      fetchCommunityRecords: true,
      showTradingWallet: true,
      showTransferPreview: true,
      showBenefitRules: false,
      showTransfer: true,
      showSmartInvestmentPurchasePercentage: true,
      accountType: '3',
      showPurchaseProductFields: false,
      defaultLocale: const Locale('es', 'ES'),
      showStaticPaymentTypes: true,
      showMentorVipLevel: false,
      marketColor: MarketColor.greenUpRedDown,
      disableUpperCasePasswordProtection: true, 
      showWithdrawHistory: true,  
      showAppUpdate: false,
      showStaticPurchasePercentage: true,
      showAddWalletAddress: false,
      showDepositeTxHash: false,
    );
