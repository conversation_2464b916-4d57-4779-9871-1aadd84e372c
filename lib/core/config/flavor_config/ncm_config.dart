import 'package:flutter/material.dart';
import 'package:sf_app/core/config/app_config.dart';
import 'package:sf_app/flavors.dart';

import '../../constants/enums.dart';

/// Sets up the configuration for the NCM flavor of the app
///
/// Takes a [kDebug] parameter to determine if running in debug mode
/// Returns an [AppConfig] instance with NCM-specific settings:
/// * Uses NCM-specific API endpoints and assets
/// * Enables trading wallet and transfer features
/// * Disables benefit rules and product fields
/// * Uses English (US) locale
/// * Uses static payment types instead of API ones
/// * Disables mentor vip level
/// * Disables upper case password protection
/// * Enables withdraw history

@Deprecated('deprecated flavor')
setupNcmConfig(bool kDebug) => AppConfig(
      flavor: Flavor.ncm,
      appName: 'NCM Financial',
      baseUrl: kDebug
          ? 'https://api.superfuture.world'
          : 'https://api.secureforexbond.com',
      marketWsUrl: kDebug
          ? 'wss://api.superfuture.world/ws'
          : 'wss://api.secureforexbond.com/ws',
      appUrl: 'https://secureforexbond.com/',
      icon: 'assets/logo/ncm/logo.svg',
      introVideo: 'assets/splash/ncm/introVideo.mp4',
      showDebugVersionTag: kDebug,
      fetchCommunityRecords: true,
      showTradingWallet: true,
      showTransferPreview: true,
      showBenefitRules: false,
      showTransfer: true,
      showSmartInvestmentPurchasePercentage: true,
      accountType: '3',
      showPurchaseProductFields: false,
      defaultLocale: const Locale('en', 'US'),
      showStaticPaymentTypes: true,
      showMentorVipLevel: false,
      marketColor: MarketColor.greenUpRedDown,
      disableUpperCasePasswordProtection: true,
      showWithdrawHistory: true,
      showAppUpdate: false, 
      showStaticPurchasePercentage: true,
      showAddWalletAddress: false,
      showDepositeTxHash: true,
    );
