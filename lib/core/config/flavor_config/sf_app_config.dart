import 'package:flutter/material.dart';
import 'package:sf_app/core/config/app_config.dart';
import 'package:sf_app/flavors.dart';

import '../../constants/enums.dart';

/// Sets up the configuration for the Super Future flavor of the app
///
/// Takes a [kDebug] parameter to determine if running in debug mode
/// Returns an [AppConfig] instance with Super Future-specific settings:
/// * Uses Super Future-specific API endpoints and assets
/// * Enables trading wallet and transfer features
/// * Disables benefit rules and product fields
/// * Uses English (US) locale
/// * Uses static payment types instead of API ones
/// * Disables mentor vip level
/// * Enables upper case password protection
/// * Disables withdraw history
setupSfAppConfig(bool kDebug) => AppConfig(
      flavor: Flavor.sf_app,
      appName: 'Super Future',
      baseUrl: kDebug
          ? 'https://api.superfuture.world'
          : 'https://api.superfuture.world',
      marketWsUrl: kDebug
          ? 'wss://api.superfuture.world/ws'
          : 'wss://api.superfuture.world/ws',
      appUrl: 'https://superfuture.world/',
      icon: 'assets/logo/sf_app/logo.svg',
      introVideo: 'assets/splash/sf_app/introVideo.mp4',
      showDebugVersionTag: true,
      fetchCommunityRecords: true,
      showTradingWallet: true,
      showTransferPreview: true,
      showBenefitRules: false,
      showTransfer: true,
      showSmartInvestmentPurchasePercentage: true,
      accountType: '3',
      showPurchaseProductFields: false,
      defaultLocale: const Locale('en', 'US'),
      showStaticPaymentTypes: true,
      showMentorVipLevel: false,
      marketColor: MarketColor.greenUpRedDown,
      disableUpperCasePasswordProtection: false,
      showWithdrawHistory: false,
      showAppUpdate: false,
      showStaticPurchasePercentage: true, 
      showAddWalletAddress: false,
      showDepositeTxHash: false,
    );
