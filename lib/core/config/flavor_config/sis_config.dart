import 'package:flutter/material.dart';
import 'package:sf_app/core/config/app_config.dart';
import 'package:sf_app/flavors.dart';

import '../../constants/enums.dart';

/// Sets up the configuration for the SIS flavor of the app
///
/// Takes a [kDebug] parameter to determine if running in debug mode
/// Returns an [AppConfig] instance with SIS-specific settings:
/// * Uses SIS-specific API endpoints and assets
/// * Disables trading wallet and transfer features
/// * Enables benefit rules and product fields
/// * Uses English (US) locale
/// * Uses dynamic payment types from API instead of static ones
/// * Enables mentor vip level
/// * Enables upper case password protection
/// * Disables withdraw history
setupSisConfig(bool kDebug) => AppConfig(
      flavor: Flavor.sis,
      appName: 'SIS',
      baseUrl: kDebug
          ? 'https://sis-api.superfuture.world'
          : 'https://api.sisinvestors.com',
      marketWsUrl: kDebug
          ? 'wss://sis-api.superfuture.world/ws'
          : 'wss://api.sisinvestors.com/ws',
      appUrl: 'https://sisinvestors.com/',
      icon: 'assets/logo/sis/logo.svg',
      introVideo: 'assets/splash/sis/introVideo.mp4',
      showDebugVersionTag: kDebug,
      fetchCommunityRecords: false,
      showTradingWallet: false,
      showTransferPreview: false,
      showBenefitRules: true,
      showTransfer: false,
      showSmartInvestmentPurchasePercentage: true,
      accountType: '1',
      showPurchaseProductFields: true,
      defaultLocale: const Locale('en', 'US'),
      showStaticPaymentTypes: false,
      showMentorVipLevel: true,
      marketColor: MarketColor.greenUpRedDown,
      disableUpperCasePasswordProtection: false,
      showWithdrawHistory: false,
      showAppUpdate: true,
      showStaticPurchasePercentage: false,
      showAddWalletAddress: false,  
      showDepositeTxHash: true,
    );
