import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:sf_app/my_app.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sf_app/core/utils/shared_preference_helper.dart';
import 'core/config/app_config.dart';
import 'core/dependency_injection/injectable.dart';
import 'core/providers/app_providers.dart';
import 'package:timezone/data/latest.dart';

import 'flavors.dart';

// Default flavor when not specified in build configuration
const String appFlavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf_app');

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set up app flavor based on build configuration
  F.appFlavor = Flavor.values.firstWhere(
    (element) => element.name == appFlavor,
    orElse: () => Flavor.sf_app, // Default to sf_app if flavor not found
  );
  
  // Initialize flavor-specific configuration
  F.setupConfig();

  // Initialize localization and date formatting
  await EasyLocalization.ensureInitialized();
  initializeDateFormatting();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize shared preferences
  await SharedPreferenceHelper().getInit();

  // Initialize dependency injection
  initializeGetIt();

  // Initialize timezone data
  initializeTimeZones();

  // Set up HydratedBloc storage for persisting bloc states
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );

  runApp(const _Translation());
}

class _Translation extends StatelessWidget {
  const _Translation();

  @override
  Widget build(BuildContext context) => EasyLocalization(
        supportedLocales: AppConfig.supportedLanguages.map((e) => e.locale).toList(),
        path: 'assets/translations',
        fallbackLocale: const Locale('en', 'US'),
        saveLocale: true,
        child: const _MultiBlocWrapper(),
      );
}

class _MultiBlocWrapper extends StatelessWidget {
  const _MultiBlocWrapper();

  @override
  Widget build(BuildContext context) => MultiBlocProvider(
        providers: AppProviders.getProviders(),
        child: const MyApps(),
      );
}
