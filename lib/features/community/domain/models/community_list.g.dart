// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'community_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CommunityListImpl _$$CommunityListImplFromJson(Map<String, dynamic> json) =>
    _$CommunityListImpl(
      code: (json['code'] as num).toInt(),
      data: CommunityData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$CommunityListImplToJson(_$CommunityListImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$CommunityDataImpl _$$CommunityDataImplFromJson(Map<String, dynamic> json) =>
    _$CommunityDataImpl(
      list: (json['list'] as List<dynamic>)
          .map((e) => CommunityDataItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$$CommunityDataImplToJson(_$CommunityDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$CommunityDataItemImpl _$$CommunityDataItemImplFromJson(
        Map<String, dynamic> json) =>
    _$CommunityDataItemImpl(
      level: (json['level'] as num).toInt(),
      orderAmount: (json['orderAmount'] as num).toInt(),
      username: json['username'] as String,
    );

Map<String, dynamic> _$$CommunityDataItemImplToJson(
        _$CommunityDataItemImpl instance) =>
    <String, dynamic>{
      'level': instance.level,
      'orderAmount': instance.orderAmount,
      'username': instance.username,
    };
