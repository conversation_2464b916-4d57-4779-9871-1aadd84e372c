// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_sub_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AgentSubUsersImpl _$$AgentSubUsersImplFromJson(Map<String, dynamic> json) =>
    _$AgentSubUsersImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : AgentSubUsersData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$AgentSubUsersImplToJson(_$AgentSubUsersImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$AgentSubUsersDataImpl _$$AgentSubUsersDataImplFromJson(
        Map<String, dynamic> json) =>
    _$AgentSubUsersDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => ListElement.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AgentSubUsersDataImplToJson(
        _$AgentSubUsersDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$ListElementImpl _$$ListElementImplFromJson(Map<String, dynamic> json) =>
    _$ListElementImpl(
      balance: (json['balance'] as num?)?.toDouble(),
      userId: (json['userId'] as num?)?.toInt(),
      userName: json['userName'] as String?,
    );

Map<String, dynamic> _$$ListElementImplToJson(_$ListElementImpl instance) =>
    <String, dynamic>{
      'balance': instance.balance,
      'userId': instance.userId,
      'userName': instance.userName,
    };
