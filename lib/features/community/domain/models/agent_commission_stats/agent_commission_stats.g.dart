// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_commission_stats.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AgentCommissionStatsImpl _$$AgentCommissionStatsImplFromJson(
        Map<String, dynamic> json) =>
    _$AgentCommissionStatsImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : AgentCommissionStatsData.fromJson(
              json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$AgentCommissionStatsImplToJson(
        _$AgentCommissionStatsImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$AgentCommissionStatsDataImpl _$$AgentCommissionStatsDataImplFromJson(
        Map<String, dynamic> json) =>
    _$AgentCommissionStatsDataImpl(
      directMembers: (json['directMembers'] as num?)?.toInt(),
      firstGeneration: json['firstGeneration'] == null
          ? null
          : AgentCommissionStatsGeneration.fromJson(
              json['firstGeneration'] as Map<String, dynamic>),
      otherMembers: (json['otherMembers'] as num?)?.toInt(),
      secondGeneration: json['secondGeneration'] == null
          ? null
          : AgentCommissionStatsGeneration.fromJson(
              json['secondGeneration'] as Map<String, dynamic>),
      thirdGeneration: json['thirdGeneration'] == null
          ? null
          : AgentCommissionStatsGeneration.fromJson(
              json['thirdGeneration'] as Map<String, dynamic>),
      totalMembers: (json['totalMembers'] as num?)?.toInt(),
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$AgentCommissionStatsDataImplToJson(
        _$AgentCommissionStatsDataImpl instance) =>
    <String, dynamic>{
      'directMembers': instance.directMembers,
      'firstGeneration': instance.firstGeneration,
      'otherMembers': instance.otherMembers,
      'secondGeneration': instance.secondGeneration,
      'thirdGeneration': instance.thirdGeneration,
      'totalMembers': instance.totalMembers,
      'totalRevenue': instance.totalRevenue,
    };

_$AgentCommissionStatsGenerationImpl
    _$$AgentCommissionStatsGenerationImplFromJson(Map<String, dynamic> json) =>
        _$AgentCommissionStatsGenerationImpl(
          memberCount: (json['memberCount'] as num?)?.toInt(),
          revenue: (json['revenue'] as num?)?.toDouble(),
        );

Map<String, dynamic> _$$AgentCommissionStatsGenerationImplToJson(
        _$AgentCommissionStatsGenerationImpl instance) =>
    <String, dynamic>{
      'memberCount': instance.memberCount,
      'revenue': instance.revenue,
    };
