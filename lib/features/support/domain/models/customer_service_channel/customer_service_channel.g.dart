// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_service_channel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomerServiceChannelImpl _$$CustomerServiceChannelImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerServiceChannelImpl(
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) =>
              CustomerServiceChannelData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$CustomerServiceChannelImplToJson(
        _$CustomerServiceChannelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$CustomerServiceChannelDataImpl _$$CustomerServiceChannelDataImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerServiceChannelDataImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      content: json['content'] as String,
      icon: json['icon'] as String,
    );

Map<String, dynamic> _$$CustomerServiceChannelDataImplToJson(
        _$CustomerServiceChannelDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'content': instance.content,
      'icon': instance.icon,
    };
