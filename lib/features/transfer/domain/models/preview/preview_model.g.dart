// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'preview_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PreviewModelImpl _$$PreviewModelImplFromJson(Map<String, dynamic> json) =>
    _$PreviewModelImpl(
      code: (json['code'] as num).toInt(),
      data: PreviewData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$PreviewModelImplToJson(_$PreviewModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$PreviewDataImpl _$$PreviewDataImplFromJson(Map<String, dynamic> json) =>
    _$PreviewDataImpl(
      chargedAmount: (json['chargedAmount'] as num?)?.toDouble(),
      expectedReceive: (json['expectedReceive'] as num?)?.toDouble(),
      fee: (json['fee'] as num?)?.toDouble(),
      freeAmount: (json['freeAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$PreviewDataImplToJson(_$PreviewDataImpl instance) =>
    <String, dynamic>{
      'chargedAmount': instance.chargedAmount,
      'expectedReceive': instance.expectedReceive,
      'fee': instance.fee,
      'freeAmount': instance.freeAmount,
    };
