// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_coin.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WalletCoinImpl _$$WalletCoinImplFromJson(Map<String, dynamic> json) =>
    _$WalletCoinImpl(
      code: (json['code'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => WalletCoinData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$WalletCoinImplToJson(_$WalletCoinImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$WalletCoinDataImpl _$$WalletCoinDataImplFromJson(Map<String, dynamic> json) =>
    _$WalletCoinDataImpl(
      name: json['name'] as String?,
      code: json['code'] as String?,
      id: (json['id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$WalletCoinDataImplToJson(
        _$WalletCoinDataImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      'id': instance.id,
    };
