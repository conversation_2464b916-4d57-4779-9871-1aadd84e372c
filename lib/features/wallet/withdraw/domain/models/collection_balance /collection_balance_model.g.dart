// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collection_balance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FundingBalanceModelImpl _$$FundingBalanceModelImplFromJson(
        Map<String, dynamic> json) =>
    _$FundingBalanceModelImpl(
      code: (json['code'] as num).toInt(),
      data: FundingBalanceData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$FundingBalanceModelImplToJson(
        _$FundingBalanceModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$FundingBalanceDataImpl _$$FundingBalanceDataImplFromJson(
        Map<String, dynamic> json) =>
    _$FundingBalanceDataImpl(
      balance: json['balance'] as String,
    );

Map<String, dynamic> _$$FundingBalanceDataImplToJson(
        _$FundingBalanceDataImpl instance) =>
    <String, dynamic>{
      'balance': instance.balance,
    };
