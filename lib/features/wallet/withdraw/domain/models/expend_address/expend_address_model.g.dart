// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expend_address_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExpendAddressModelImpl _$$ExpendAddressModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ExpendAddressModelImpl(
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => ExpendAddressData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$ExpendAddressModelImplToJson(
        _$ExpendAddressModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ExpendAddressDataImpl _$$ExpendAddressDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ExpendAddressDataImpl(
      address: json['address'] as String,
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$$ExpendAddressDataImplToJson(
        _$ExpendAddressDataImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };
