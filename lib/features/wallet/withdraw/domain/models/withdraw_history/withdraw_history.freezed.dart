// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_history.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WithdrawHistory _$WithdrawHistoryFromJson(Map<String, dynamic> json) {
  return _WithdrawHistory.fromJson(json);
}

/// @nodoc
mixin _$WithdrawHistory {
  int? get code => throw _privateConstructorUsedError;
  WithdrawHistoryList? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this WithdrawHistory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WithdrawHistoryCopyWith<WithdrawHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WithdrawHistoryCopyWith<$Res> {
  factory $WithdrawHistoryCopyWith(
          WithdrawHistory value, $Res Function(WithdrawHistory) then) =
      _$WithdrawHistoryCopyWithImpl<$Res, WithdrawHistory>;
  @useResult
  $Res call({int? code, WithdrawHistoryList? data, String? msg});

  $WithdrawHistoryListCopyWith<$Res>? get data;
}

/// @nodoc
class _$WithdrawHistoryCopyWithImpl<$Res, $Val extends WithdrawHistory>
    implements $WithdrawHistoryCopyWith<$Res> {
  _$WithdrawHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as WithdrawHistoryList?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WithdrawHistoryListCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $WithdrawHistoryListCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WithdrawHistoryImplCopyWith<$Res>
    implements $WithdrawHistoryCopyWith<$Res> {
  factory _$$WithdrawHistoryImplCopyWith(_$WithdrawHistoryImpl value,
          $Res Function(_$WithdrawHistoryImpl) then) =
      __$$WithdrawHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, WithdrawHistoryList? data, String? msg});

  @override
  $WithdrawHistoryListCopyWith<$Res>? get data;
}

/// @nodoc
class __$$WithdrawHistoryImplCopyWithImpl<$Res>
    extends _$WithdrawHistoryCopyWithImpl<$Res, _$WithdrawHistoryImpl>
    implements _$$WithdrawHistoryImplCopyWith<$Res> {
  __$$WithdrawHistoryImplCopyWithImpl(
      _$WithdrawHistoryImpl _value, $Res Function(_$WithdrawHistoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$WithdrawHistoryImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as WithdrawHistoryList?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WithdrawHistoryImpl implements _WithdrawHistory {
  const _$WithdrawHistoryImpl({this.code, this.data, this.msg});

  factory _$WithdrawHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$WithdrawHistoryImplFromJson(json);

  @override
  final int? code;
  @override
  final WithdrawHistoryList? data;
  @override
  final String? msg;

  @override
  String toString() {
    return 'WithdrawHistory(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WithdrawHistoryImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WithdrawHistoryImplCopyWith<_$WithdrawHistoryImpl> get copyWith =>
      __$$WithdrawHistoryImplCopyWithImpl<_$WithdrawHistoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WithdrawHistoryImplToJson(
      this,
    );
  }
}

abstract class _WithdrawHistory implements WithdrawHistory {
  const factory _WithdrawHistory(
      {final int? code,
      final WithdrawHistoryList? data,
      final String? msg}) = _$WithdrawHistoryImpl;

  factory _WithdrawHistory.fromJson(Map<String, dynamic> json) =
      _$WithdrawHistoryImpl.fromJson;

  @override
  int? get code;
  @override
  WithdrawHistoryList? get data;
  @override
  String? get msg;

  /// Create a copy of WithdrawHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WithdrawHistoryImplCopyWith<_$WithdrawHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WithdrawHistoryList _$WithdrawHistoryListFromJson(Map<String, dynamic> json) {
  return _WithdrawHistoryList.fromJson(json);
}

/// @nodoc
mixin _$WithdrawHistoryList {
  List<WithdrawHistoryItem>? get list => throw _privateConstructorUsedError;
  int? get pageNum => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  /// Serializes this WithdrawHistoryList to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WithdrawHistoryList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WithdrawHistoryListCopyWith<WithdrawHistoryList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WithdrawHistoryListCopyWith<$Res> {
  factory $WithdrawHistoryListCopyWith(
          WithdrawHistoryList value, $Res Function(WithdrawHistoryList) then) =
      _$WithdrawHistoryListCopyWithImpl<$Res, WithdrawHistoryList>;
  @useResult
  $Res call(
      {List<WithdrawHistoryItem>? list,
      int? pageNum,
      int? pageSize,
      int? total});
}

/// @nodoc
class _$WithdrawHistoryListCopyWithImpl<$Res, $Val extends WithdrawHistoryList>
    implements $WithdrawHistoryListCopyWith<$Res> {
  _$WithdrawHistoryListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WithdrawHistoryList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      list: freezed == list
          ? _value.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<WithdrawHistoryItem>?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WithdrawHistoryListImplCopyWith<$Res>
    implements $WithdrawHistoryListCopyWith<$Res> {
  factory _$$WithdrawHistoryListImplCopyWith(_$WithdrawHistoryListImpl value,
          $Res Function(_$WithdrawHistoryListImpl) then) =
      __$$WithdrawHistoryListImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<WithdrawHistoryItem>? list,
      int? pageNum,
      int? pageSize,
      int? total});
}

/// @nodoc
class __$$WithdrawHistoryListImplCopyWithImpl<$Res>
    extends _$WithdrawHistoryListCopyWithImpl<$Res, _$WithdrawHistoryListImpl>
    implements _$$WithdrawHistoryListImplCopyWith<$Res> {
  __$$WithdrawHistoryListImplCopyWithImpl(_$WithdrawHistoryListImpl _value,
      $Res Function(_$WithdrawHistoryListImpl) _then)
      : super(_value, _then);

  /// Create a copy of WithdrawHistoryList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = freezed,
    Object? pageNum = freezed,
    Object? pageSize = freezed,
    Object? total = freezed,
  }) {
    return _then(_$WithdrawHistoryListImpl(
      list: freezed == list
          ? _value._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<WithdrawHistoryItem>?,
      pageNum: freezed == pageNum
          ? _value.pageNum
          : pageNum // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WithdrawHistoryListImpl implements _WithdrawHistoryList {
  const _$WithdrawHistoryListImpl(
      {final List<WithdrawHistoryItem>? list,
      this.pageNum,
      this.pageSize,
      this.total})
      : _list = list;

  factory _$WithdrawHistoryListImpl.fromJson(Map<String, dynamic> json) =>
      _$$WithdrawHistoryListImplFromJson(json);

  final List<WithdrawHistoryItem>? _list;
  @override
  List<WithdrawHistoryItem>? get list {
    final value = _list;
    if (value == null) return null;
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? pageNum;
  @override
  final int? pageSize;
  @override
  final int? total;

  @override
  String toString() {
    return 'WithdrawHistoryList(list: $list, pageNum: $pageNum, pageSize: $pageSize, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WithdrawHistoryListImpl &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            (identical(other.pageNum, pageNum) || other.pageNum == pageNum) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_list), pageNum, pageSize, total);

  /// Create a copy of WithdrawHistoryList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WithdrawHistoryListImplCopyWith<_$WithdrawHistoryListImpl> get copyWith =>
      __$$WithdrawHistoryListImplCopyWithImpl<_$WithdrawHistoryListImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WithdrawHistoryListImplToJson(
      this,
    );
  }
}

abstract class _WithdrawHistoryList implements WithdrawHistoryList {
  const factory _WithdrawHistoryList(
      {final List<WithdrawHistoryItem>? list,
      final int? pageNum,
      final int? pageSize,
      final int? total}) = _$WithdrawHistoryListImpl;

  factory _WithdrawHistoryList.fromJson(Map<String, dynamic> json) =
      _$WithdrawHistoryListImpl.fromJson;

  @override
  List<WithdrawHistoryItem>? get list;
  @override
  int? get pageNum;
  @override
  int? get pageSize;
  @override
  int? get total;

  /// Create a copy of WithdrawHistoryList
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WithdrawHistoryListImplCopyWith<_$WithdrawHistoryListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WithdrawHistoryItem _$WithdrawHistoryItemFromJson(Map<String, dynamic> json) {
  return _WithdrawHistoryItem.fromJson(json);
}

/// @nodoc
mixin _$WithdrawHistoryItem {
  String? get address => throw _privateConstructorUsedError;
  int? get amount => throw _privateConstructorUsedError;
  String? get completeTime => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this WithdrawHistoryItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WithdrawHistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WithdrawHistoryItemCopyWith<WithdrawHistoryItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WithdrawHistoryItemCopyWith<$Res> {
  factory $WithdrawHistoryItemCopyWith(
          WithdrawHistoryItem value, $Res Function(WithdrawHistoryItem) then) =
      _$WithdrawHistoryItemCopyWithImpl<$Res, WithdrawHistoryItem>;
  @useResult
  $Res call(
      {String? address,
      int? amount,
      String? completeTime,
      String? createTime,
      int? status,
      int? userId});
}

/// @nodoc
class _$WithdrawHistoryItemCopyWithImpl<$Res, $Val extends WithdrawHistoryItem>
    implements $WithdrawHistoryItemCopyWith<$Res> {
  _$WithdrawHistoryItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WithdrawHistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? amount = freezed,
    Object? completeTime = freezed,
    Object? createTime = freezed,
    Object? status = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      completeTime: freezed == completeTime
          ? _value.completeTime
          : completeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WithdrawHistoryItemImplCopyWith<$Res>
    implements $WithdrawHistoryItemCopyWith<$Res> {
  factory _$$WithdrawHistoryItemImplCopyWith(_$WithdrawHistoryItemImpl value,
          $Res Function(_$WithdrawHistoryItemImpl) then) =
      __$$WithdrawHistoryItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? address,
      int? amount,
      String? completeTime,
      String? createTime,
      int? status,
      int? userId});
}

/// @nodoc
class __$$WithdrawHistoryItemImplCopyWithImpl<$Res>
    extends _$WithdrawHistoryItemCopyWithImpl<$Res, _$WithdrawHistoryItemImpl>
    implements _$$WithdrawHistoryItemImplCopyWith<$Res> {
  __$$WithdrawHistoryItemImplCopyWithImpl(_$WithdrawHistoryItemImpl _value,
      $Res Function(_$WithdrawHistoryItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of WithdrawHistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? amount = freezed,
    Object? completeTime = freezed,
    Object? createTime = freezed,
    Object? status = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$WithdrawHistoryItemImpl(
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      completeTime: freezed == completeTime
          ? _value.completeTime
          : completeTime // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WithdrawHistoryItemImpl implements _WithdrawHistoryItem {
  const _$WithdrawHistoryItemImpl(
      {this.address,
      this.amount,
      this.completeTime,
      this.createTime,
      this.status,
      this.userId});

  factory _$WithdrawHistoryItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$WithdrawHistoryItemImplFromJson(json);

  @override
  final String? address;
  @override
  final int? amount;
  @override
  final String? completeTime;
  @override
  final String? createTime;
  @override
  final int? status;
  @override
  final int? userId;

  @override
  String toString() {
    return 'WithdrawHistoryItem(address: $address, amount: $amount, completeTime: $completeTime, createTime: $createTime, status: $status, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WithdrawHistoryItemImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.completeTime, completeTime) ||
                other.completeTime == completeTime) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, address, amount, completeTime, createTime, status, userId);

  /// Create a copy of WithdrawHistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WithdrawHistoryItemImplCopyWith<_$WithdrawHistoryItemImpl> get copyWith =>
      __$$WithdrawHistoryItemImplCopyWithImpl<_$WithdrawHistoryItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WithdrawHistoryItemImplToJson(
      this,
    );
  }
}

abstract class _WithdrawHistoryItem implements WithdrawHistoryItem {
  const factory _WithdrawHistoryItem(
      {final String? address,
      final int? amount,
      final String? completeTime,
      final String? createTime,
      final int? status,
      final int? userId}) = _$WithdrawHistoryItemImpl;

  factory _WithdrawHistoryItem.fromJson(Map<String, dynamic> json) =
      _$WithdrawHistoryItemImpl.fromJson;

  @override
  String? get address;
  @override
  int? get amount;
  @override
  String? get completeTime;
  @override
  String? get createTime;
  @override
  int? get status;
  @override
  int? get userId;

  /// Create a copy of WithdrawHistoryItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WithdrawHistoryItemImplCopyWith<_$WithdrawHistoryItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
