// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdraw_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WithdrawHistoryImpl _$$WithdrawHistoryImplFromJson(
        Map<String, dynamic> json) =>
    _$WithdrawHistoryImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : WithdrawHistoryList.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$WithdrawHistoryImplToJson(
        _$WithdrawHistoryImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$WithdrawHistoryListImpl _$$WithdrawHistoryListImplFromJson(
        Map<String, dynamic> json) =>
    _$WithdrawHistoryListImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => WithdrawHistoryItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$WithdrawHistoryListImplToJson(
        _$WithdrawHistoryListImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$WithdrawHistoryItemImpl _$$WithdrawHistoryItemImplFromJson(
        Map<String, dynamic> json) =>
    _$WithdrawHistoryItemImpl(
      address: json['address'] as String?,
      amount: (json['amount'] as num?)?.toInt(),
      completeTime: json['completeTime'] as String?,
      createTime: json['createTime'] as String?,
      status: (json['status'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$WithdrawHistoryItemImplToJson(
        _$WithdrawHistoryItemImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'amount': instance.amount,
      'completeTime': instance.completeTime,
      'createTime': instance.createTime,
      'status': instance.status,
      'userId': instance.userId,
    };
