import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/constants/string_constants.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/widgets/pagination_widget.dart';
import 'package:sf_app/features/wallet/withdraw/domain/models/withdraw_history/withdraw_history.dart';
import 'package:sf_app/features/wallet/withdraw/logic/withdraw/withdraw_cubit.dart';
import 'package:sf_app/features/wallet/withdraw/widgets/withdraw_history_item.dart';

import '../../../../core/widgets/common_empty_data.dart';

class WithdrawHistoryScreen extends StatefulWidget {
  final bool showBackButton;

  const WithdrawHistoryScreen({
    super.key,
    this.showBackButton = true,
  });

  @override
  State<WithdrawHistoryScreen> createState() => _WithdrawHistoryScreenState();
}

class _WithdrawHistoryScreenState extends State<WithdrawHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  /// - [status] The status of the withdrawal ([0]: [Pending], [1]: [Processing], [2]: [Success], [3]: [Rejected])
  final List<String> tabTitles = [
    StringConstants.pending, // 0: Pending
    StringConstants.processing, // 1: Processing
    StringConstants.completed, // 2: Success
    StringConstants.rejected, // 3: Rejected
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabTitles.length, vsync: this);
    _tabController.addListener(_handleTabChange);
    _fetchAllRecords();
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _fetchRecords();
    }
  }

  // Map tab index to backend status
  int _getStatus(int tabIndex) => switch (tabIndex) {
        0 => 0, // pending
        1 => 1, // processing
        2 => 2, // success
        3 => 3, // rejected
        _ => 0
      };

  void _fetchRecords() {
    if (mounted) {
      context.read<WithdrawCubit>().getWithdrawHistory(
            status: _getStatus(_tabController.index),
          );
    }
  }

  void _fetchAllRecords() {
    if (mounted) {
      context.read<WithdrawCubit>()
        ..getWithdrawHistory(status: _getStatus(0))
        ..getWithdrawHistory(status: _getStatus(1))
        ..getWithdrawHistory(status: _getStatus(2))
        ..getWithdrawHistory(status: _getStatus(3));
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          StringConstants.withdrawHistory.tr(),
          style: FontPalette.semiBold20.copyWith(
            color: myColorScheme(context).titleColor,
          ),
        ),
        centerTitle: true,
        leading: widget.showBackButton
            ? IconButton(
                onPressed: () => Navigator.of(context).maybePop(),
                icon: Icon(
                  Icons.arrow_back_ios_new_rounded,
                  color: myColorScheme(context).appBarIconColor,
                ),
              )
            : null,
      ),
      body: BlocBuilder<WithdrawCubit, WithdrawState>(
        builder: (context, state) {
          if (state.withdrawHistoryFetchStatus == DataStatus.loading &&
              state.withdrawHistoryByStatus.isEmpty) {
            return _buildShimmerList();
          }
          return Column(
            children: [
              TabBar(
                indicatorColor: myColorScheme(context).primaryColor,
                indicatorWeight: 1,
                indicatorSize: TabBarIndicatorSize.label,
                labelPadding: EdgeInsets.zero,
                dividerHeight: .5,
                dividerColor: myColorScheme(context).greyColor3,
                controller: _tabController,
                tabs: List.generate(
                  tabTitles.length,
                  (index) => Tab(
                    child: Text(
                      tabTitles[index].tr(),
                      style: FontPalette.medium16.copyWith(
                        color: _getStatusColor(_getStatus(index)),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: List.generate(tabTitles.length, (index) {
                    int status = _getStatus(index);
                    return RefreshIndicator(
                      onRefresh: () =>
                          context.read<WithdrawCubit>().getWithdrawHistory(
                                status: status,
                              ),
                      child: state.withdrawHistoryByStatus[status]?.data?.list
                                  ?.isEmpty ??
                              true
                          ? _buildEmptyState()
                          : _buildWithdrawList(
                              state.withdrawHistoryByStatus[status]?.data
                                      ?.list ??
                                  [],
                              state,
                            ),
                    );
                  }),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildWithdrawList(
    List<WithdrawHistoryItem> withdrawList,
    WithdrawState state,
  ) {
    final currentStatus = _getStatus(_tabController.index);
    final currentStatusData = state.withdrawHistoryByStatus[currentStatus];

    return PaginationWidget(
      isPaginating: state.withdrawHistoryFetchStatus == DataStatus.loading &&
          _hasMoreData(currentStatusData),
      next: _hasMoreData(currentStatusData),
      onPagination: (notification) =>
          _handlePagination(context, currentStatus, currentStatusData),
      child: ListView.builder(
        padding: EdgeInsets.all(16.r),
        itemCount: withdrawList.length,
        itemBuilder: (context, index) {
          final item = withdrawList[index];
          return WithdrawHistoryItemWidget(
            item: item,
            index: index,
          );
        },
      ),
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5, // Show 5 shimmer items
      itemBuilder: (_, __) => _buildShimmerCard(),
    );
  }

  Widget _buildShimmerCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 80.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              Container(
                width: 60.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
            ],
          ),
          16.verticalSpace,
          // Details rows
          ...List.generate(
            3,
            (index) => Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 100.w,
                    height: 14.h,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  Container(
                    width: 80.w,
                    height: 14.h,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() => const Center(
        child: CommonEmpty(),
      );

  Color _getStatusColor(int status) => switch (status) {
        0 => const Color(0xFFFAAD14), // Pending (Orange)
        1 => const Color(0xFF1677FF), // Processing (Blue)
        2 => const Color(0xFF52C41A), // Completed (Green)
        3 => const Color(0xFFFF4D4F), // Rejected (Red)
        _ => const Color(0xFF1677FF), // Default (Blue)
      };

  bool _hasMoreData(WithdrawHistory? statusData) {
    final currentCount = statusData?.data?.list?.length ?? 0;
    final totalCount = statusData?.data?.total ?? 0;
    return currentCount < totalCount;
  }

  bool _handlePagination(
      BuildContext context, int status, WithdrawHistory? statusData) {
    if (!_hasMoreData(statusData)) {
      return false;
    }

    context.read<WithdrawCubit>().getWithdrawHistory(
          page: (statusData?.data?.pageNum ?? 0) + 1,
          status: status,
          isLoadMore: true,
        );
    return true;
  }
}
