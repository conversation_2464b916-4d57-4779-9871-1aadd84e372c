// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'records_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecordsModelImpl _$$RecordsModelImplFromJson(Map<String, dynamic> json) =>
    _$RecordsModelImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : RecordListData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$RecordsModelImplToJson(_$RecordsModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$RecordListDataImpl _$$RecordListDataImplFromJson(Map<String, dynamic> json) =>
    _$RecordListDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => RecordData.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RecordListDataImplToJson(
        _$RecordListDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$RecordDataImpl _$$RecordDataImplFromJson(Map<String, dynamic> json) =>
    _$RecordDataImpl(
      amount: (json['amount'] as num?)?.toDouble(),
      balance: (json['balance'] as num?)?.toDouble(),
      createTime: json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
      fundDirection:
          $enumDecodeNullable(_$FundDirectionEnumMap, json['fundDirection']),
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$RecordDataImplToJson(_$RecordDataImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'balance': instance.balance,
      'createTime': instance.createTime?.toIso8601String(),
      'fundDirection': _$FundDirectionEnumMap[instance.fundDirection],
      'title': instance.title,
    };

const _$FundDirectionEnumMap = {
  FundDirection.sub: 'SUB',
  FundDirection.add: 'ADD',
};
