// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'news_update_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NewsItemImpl _$$NewsItemImplFromJson(Map<String, dynamic> json) =>
    _$NewsItemImpl(
      code: (json['code'] as num).toInt(),
      data: NewsItemData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$NewsItemImplToJson(_$NewsItemImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$NewsItemDataImpl _$$NewsItemDataImplFromJson(Map<String, dynamic> json) =>
    _$NewsItemDataImpl(
      content: json['content'] as String,
      coverUrl: json['coverUrl'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
      id: (json['id'] as num).toInt(),
      imgUrl: json['imgUrl'] as String,
      modifiedTime: json['modifiedTime'],
      remark: json['remark'] as String,
      status: (json['status'] as num).toInt(),
      title: json['title'] as String,
      type: (json['type'] as num).toInt(),
    );

Map<String, dynamic> _$$NewsItemDataImplToJson(_$NewsItemDataImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
      'coverUrl': instance.coverUrl,
      'createTime': instance.createTime.toIso8601String(),
      'id': instance.id,
      'imgUrl': instance.imgUrl,
      'modifiedTime': instance.modifiedTime,
      'remark': instance.remark,
      'status': instance.status,
      'title': instance.title,
      'type': instance.type,
    };
