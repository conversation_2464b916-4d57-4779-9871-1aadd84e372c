// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_update_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppUpdateModelImpl _$$AppUpdateModelImplFromJson(Map<String, dynamic> json) =>
    _$AppUpdateModelImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : AppUpdateData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$AppUpdateModelImplToJson(
        _$AppUpdateModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$AppUpdateDataImpl _$$AppUpdateDataImplFromJson(Map<String, dynamic> json) =>
    _$AppUpdateDataImpl(
      fileUrl: json['fileUrl'] as String?,
      forceUpdate: json['forceUpdate'] as bool?,
      platform: json['platform'] as String?,
      status: (json['status'] as num?)?.toInt(),
      updateContent: json['updateContent'] as String?,
      versionCode: (json['versionCode'] as num?)?.toInt(),
      versionName: json['versionName'] as String?,
    );

Map<String, dynamic> _$$AppUpdateDataImplToJson(_$AppUpdateDataImpl instance) =>
    <String, dynamic>{
      'fileUrl': instance.fileUrl,
      'forceUpdate': instance.forceUpdate,
      'platform': instance.platform,
      'status': instance.status,
      'updateContent': instance.updateContent,
      'versionCode': instance.versionCode,
      'versionName': instance.versionName,
    };
