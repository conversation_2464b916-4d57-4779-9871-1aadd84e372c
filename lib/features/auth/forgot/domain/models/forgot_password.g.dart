// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'forgot_password.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ForgotPasswordImpl _$$ForgotPasswordImplFromJson(Map<String, dynamic> json) =>
    _$ForgotPasswordImpl(
      code: (json['code'] as num).toInt(),
      data: ForgotPasswordData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$ForgotPasswordImplToJson(
        _$ForgotPasswordImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ForgotPasswordDataImpl _$$ForgotPasswordDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ForgotPasswordDataImpl(
      nonce: json['nonce'] as String,
    );

Map<String, dynamic> _$$ForgotPasswordDataImplToJson(
        _$ForgotPasswordDataImpl instance) =>
    <String, dynamic>{
      'nonce': instance.nonce,
    };
