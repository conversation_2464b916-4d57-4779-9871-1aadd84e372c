// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchased_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PurchasedListImpl _$$PurchasedListImplFromJson(Map<String, dynamic> json) =>
    _$PurchasedListImpl(
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => PurchasedListData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String,
    );

Map<String, dynamic> _$$PurchasedListImplToJson(_$PurchasedListImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$PurchasedListDataImpl _$$PurchasedListDataImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchasedListDataImpl(
      buyPrice: json['buyPrice'],
      createTime: DateTime.parse(json['createTime'] as String),
      id: json['id'],
      orderAmount: (json['orderAmount'] as num).toDouble(),
      orderNo: json['orderNo'] as String,
      sellPrice: json['sellPrice'],
      status: (json['status'] as num).toInt(),
      stockName: json['stockName'] as String,
      totalProfit: json['totalProfit'],
    );

Map<String, dynamic> _$$PurchasedListDataImplToJson(
        _$PurchasedListDataImpl instance) =>
    <String, dynamic>{
      'buyPrice': instance.buyPrice,
      'createTime': instance.createTime.toIso8601String(),
      'id': instance.id,
      'orderAmount': instance.orderAmount,
      'orderNo': instance.orderNo,
      'sellPrice': instance.sellPrice,
      'status': instance.status,
      'stockName': instance.stockName,
      'totalProfit': instance.totalProfit,
    };
