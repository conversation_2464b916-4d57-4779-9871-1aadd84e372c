// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'investment_records.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InvestmentRecordImpl _$$InvestmentRecordImplFromJson(
        Map<String, dynamic> json) =>
    _$InvestmentRecordImpl(
      applyCreateTime: json['applyCreateTime'] as String?,
      auditContent: json['auditContent'] as String?,
      commissionRate: (json['commissionRate'] as num?)?.toInt(),
      cycle: (json['cycle'] as num?)?.toInt(),
      dailyCanWithdrawAmount:
          (json['dailyCanWithdrawAmount'] as num?)?.toDouble(),
      expireTime: json['expireTime'] as String?,
      id: (json['id'] as num?)?.toInt(),
      maxAmount: (json['maxAmount'] as num?)?.toDouble(),
      mentorName: json['mentorName'] as String?,
      minAmount: (json['minAmount'] as num?)?.toDouble(),
      orderNo: json['orderNo'] as String?,
      processStatus: (json['processStatus'] as num?)?.toInt(),
      productName: json['productName'] as String?,
      singleAmount: (json['singleAmount'] as num?)?.toDouble(),
      startTime: json['startTime'] as String?,
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$InvestmentRecordImplToJson(
        _$InvestmentRecordImpl instance) =>
    <String, dynamic>{
      'applyCreateTime': instance.applyCreateTime,
      'auditContent': instance.auditContent,
      'commissionRate': instance.commissionRate,
      'cycle': instance.cycle,
      'dailyCanWithdrawAmount': instance.dailyCanWithdrawAmount,
      'expireTime': instance.expireTime,
      'id': instance.id,
      'maxAmount': instance.maxAmount,
      'mentorName': instance.mentorName,
      'minAmount': instance.minAmount,
      'orderNo': instance.orderNo,
      'processStatus': instance.processStatus,
      'productName': instance.productName,
      'singleAmount': instance.singleAmount,
      'startTime': instance.startTime,
      'totalAmount': instance.totalAmount,
    };

_$RecordDataImpl _$$RecordDataImplFromJson(Map<String, dynamic> json) =>
    _$RecordDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => InvestmentRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RecordDataImplToJson(_$RecordDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$InvestmentRecordModelImpl _$$InvestmentRecordModelImplFromJson(
        Map<String, dynamic> json) =>
    _$InvestmentRecordModelImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : RecordData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$InvestmentRecordModelImplToJson(
        _$InvestmentRecordModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };
