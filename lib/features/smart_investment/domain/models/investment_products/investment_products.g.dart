// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'investment_products.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductResponseImpl _$$ProductResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$ProductResponseImplToJson(
        _$ProductResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ProductImpl _$$ProductImplFromJson(Map<String, dynamic> json) =>
    _$ProductImpl(
      commissionRate: (json['commissionRate'] as num?)?.toDouble(),
      cycle: (json['cycle'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      maxAmount: (json['maxAmount'] as num?)?.toDouble(),
      mentor: json['mentor'] as String?,
      minAmount: (json['minAmount'] as num?)?.toDouble(),
      name: json['name'] as String?,
      singleAmount: (json['singleAmount'] as num?)?.toDouble(),
      type: (json['type'] as num?)?.toInt(),
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductImplToJson(_$ProductImpl instance) =>
    <String, dynamic>{
      'commissionRate': instance.commissionRate,
      'cycle': instance.cycle,
      'id': instance.id,
      'maxAmount': instance.maxAmount,
      'mentor': instance.mentor,
      'minAmount': instance.minAmount,
      'name': instance.name,
      'singleAmount': instance.singleAmount,
      'type': instance.type,
      'sort': instance.sort,
    };
