// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'investment_respone.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InvestmentResponseImpl _$$InvestmentResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$InvestmentResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$InvestmentResponseImplToJson(
        _$InvestmentResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      additionalMount: json['additionalMount'],
      applyCreateTime: (json['applyCreateTime'] as num?)?.toInt(),
      auditContent: json['auditContent'] as String?,
      commissionRate: (json['commissionRate'] as num?)?.toInt(),
      cycle: (json['cycle'] as num?)?.toInt(),
      dailyCanWithdrawAmount: json['dailyCanWithdrawAmount'],
      expireTime: json['expireTime'] as String?,
      id: (json['id'] as num?)?.toInt(),
      maxAmount: json['maxAmount'],
      mentorId: (json['mentorId'] as num?)?.toInt(),
      mentorName: json['mentorName'] as String?,
      minAmount: json['minAmount'],
      orderNo: json['orderNo'] as String?,
      processStatus: json['processStatus'],
      productName: json['productName'] as String?,
      singleAmount: json['singleAmount'],
      startTime: json['startTime'] as String?,
      totalAmount: (json['totalAmount'] as num?)?.toInt(),
      productId: (json['productId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'additionalMount': instance.additionalMount,
      'applyCreateTime': instance.applyCreateTime,
      'auditContent': instance.auditContent,
      'commissionRate': instance.commissionRate,
      'cycle': instance.cycle,
      'dailyCanWithdrawAmount': instance.dailyCanWithdrawAmount,
      'expireTime': instance.expireTime,
      'id': instance.id,
      'maxAmount': instance.maxAmount,
      'mentorId': instance.mentorId,
      'mentorName': instance.mentorName,
      'minAmount': instance.minAmount,
      'orderNo': instance.orderNo,
      'processStatus': instance.processStatus,
      'productName': instance.productName,
      'singleAmount': instance.singleAmount,
      'startTime': instance.startTime,
      'totalAmount': instance.totalAmount,
      'productId': instance.productId,
    };
