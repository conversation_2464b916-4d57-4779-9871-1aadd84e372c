// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_detail_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecordDetailResponseImpl _$$RecordDetailResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$RecordDetailResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : RecordDetailMainData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$RecordDetailResponseImplToJson(
        _$RecordDetailResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$RecordDetailMainDataImpl _$$RecordDetailMainDataImplFromJson(
        Map<String, dynamic> json) =>
    _$RecordDetailMainDataImpl(
      additionalAmount: (json['additionalAmount'] as num?)?.toDouble(),
      infoList: (json['infoList'] as List<dynamic>?)
          ?.map((e) => RecordDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderAmount: (json['orderAmount'] as num?)?.toDouble(),
      orderNo: json['orderNo'] as String?,
      rejectAmount: (json['rejectAmount'] as num?)?.toDouble(),
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$RecordDetailMainDataImplToJson(
        _$RecordDetailMainDataImpl instance) =>
    <String, dynamic>{
      'additionalAmount': instance.additionalAmount,
      'infoList': instance.infoList,
      'orderAmount': instance.orderAmount,
      'orderNo': instance.orderNo,
      'rejectAmount': instance.rejectAmount,
      'totalAmount': instance.totalAmount,
    };

_$RecordDetailImpl _$$RecordDetailImplFromJson(Map<String, dynamic> json) =>
    _$RecordDetailImpl(
      buyDate: json['buyDate'],
      buyPosition: json['buyPosition'] as String?,
      buyPrice: (json['buyPrice'] as num?)?.toDouble(),
      buyQuantity: (json['buyQuantity'] as num?)?.toDouble(),
      mentorCommission: (json['mentorCommission'] as num?)?.toDouble(),
      netProfit: (json['netProfit'] as num?)?.toDouble(),
      orderNo: json['orderNo'] as String?,
      platformCommission: (json['platformCommission'] as num?)?.toDouble(),
      sellDate: json['sellDate'],
      sellPrice: (json['sellPrice'] as num?)?.toDouble(),
      sellQuantity: (json['sellQuantity'] as num?)?.toDouble(),
      stockCode: json['stockCode'] as String?,
      stockName: json['stockName'] as String?,
      totalProfit: (json['totalProfit'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$RecordDetailImplToJson(_$RecordDetailImpl instance) =>
    <String, dynamic>{
      'buyDate': instance.buyDate,
      'buyPosition': instance.buyPosition,
      'buyPrice': instance.buyPrice,
      'buyQuantity': instance.buyQuantity,
      'mentorCommission': instance.mentorCommission,
      'netProfit': instance.netProfit,
      'orderNo': instance.orderNo,
      'platformCommission': instance.platformCommission,
      'sellDate': instance.sellDate,
      'sellPrice': instance.sellPrice,
      'sellQuantity': instance.sellQuantity,
      'stockCode': instance.stockCode,
      'stockName': instance.stockName,
      'totalProfit': instance.totalProfit,
    };
