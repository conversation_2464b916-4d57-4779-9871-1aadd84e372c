// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_rate.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderRate _$OrderRateFromJson(Map<String, dynamic> json) {
  return _OrderRate.fromJson(json);
}

/// @nodoc
mixin _$OrderRate {
  int? get code => throw _privateConstructorUsedError;
  OrderRateData? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this OrderRate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderRateCopyWith<OrderRate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderRateCopyWith<$Res> {
  factory $OrderRateCopyWith(OrderRate value, $Res Function(OrderRate) then) =
      _$OrderRateCopyWithImpl<$Res, OrderRate>;
  @useResult
  $Res call({int? code, OrderRateData? data, String? msg});

  $OrderRateDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$OrderRateCopyWithImpl<$Res, $Val extends OrderRate>
    implements $OrderRateCopyWith<$Res> {
  _$OrderRateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as OrderRateData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderRateDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $OrderRateDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderRateImplCopyWith<$Res>
    implements $OrderRateCopyWith<$Res> {
  factory _$$OrderRateImplCopyWith(
          _$OrderRateImpl value, $Res Function(_$OrderRateImpl) then) =
      __$$OrderRateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, OrderRateData? data, String? msg});

  @override
  $OrderRateDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$OrderRateImplCopyWithImpl<$Res>
    extends _$OrderRateCopyWithImpl<$Res, _$OrderRateImpl>
    implements _$$OrderRateImplCopyWith<$Res> {
  __$$OrderRateImplCopyWithImpl(
      _$OrderRateImpl _value, $Res Function(_$OrderRateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$OrderRateImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as OrderRateData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderRateImpl implements _OrderRate {
  const _$OrderRateImpl({this.code, this.data, this.msg});

  factory _$OrderRateImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderRateImplFromJson(json);

  @override
  final int? code;
  @override
  final OrderRateData? data;
  @override
  final String? msg;

  @override
  String toString() {
    return 'OrderRate(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderRateImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderRateImplCopyWith<_$OrderRateImpl> get copyWith =>
      __$$OrderRateImplCopyWithImpl<_$OrderRateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderRateImplToJson(
      this,
    );
  }
}

abstract class _OrderRate implements OrderRate {
  const factory _OrderRate(
      {final int? code,
      final OrderRateData? data,
      final String? msg}) = _$OrderRateImpl;

  factory _OrderRate.fromJson(Map<String, dynamic> json) =
      _$OrderRateImpl.fromJson;

  @override
  int? get code;
  @override
  OrderRateData? get data;
  @override
  String? get msg;

  /// Create a copy of OrderRate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderRateImplCopyWith<_$OrderRateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderRateData _$OrderRateDataFromJson(Map<String, dynamic> json) {
  return _OrderRateData.fromJson(json);
}

/// @nodoc
mixin _$OrderRateData {
  List<int>? get orderRates => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this OrderRateData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderRateData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderRateDataCopyWith<OrderRateData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderRateDataCopyWith<$Res> {
  factory $OrderRateDataCopyWith(
          OrderRateData value, $Res Function(OrderRateData) then) =
      _$OrderRateDataCopyWithImpl<$Res, OrderRateData>;
  @useResult
  $Res call({List<int>? orderRates, int? userId});
}

/// @nodoc
class _$OrderRateDataCopyWithImpl<$Res, $Val extends OrderRateData>
    implements $OrderRateDataCopyWith<$Res> {
  _$OrderRateDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderRateData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderRates = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      orderRates: freezed == orderRates
          ? _value.orderRates
          : orderRates // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderRateDataImplCopyWith<$Res>
    implements $OrderRateDataCopyWith<$Res> {
  factory _$$OrderRateDataImplCopyWith(
          _$OrderRateDataImpl value, $Res Function(_$OrderRateDataImpl) then) =
      __$$OrderRateDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<int>? orderRates, int? userId});
}

/// @nodoc
class __$$OrderRateDataImplCopyWithImpl<$Res>
    extends _$OrderRateDataCopyWithImpl<$Res, _$OrderRateDataImpl>
    implements _$$OrderRateDataImplCopyWith<$Res> {
  __$$OrderRateDataImplCopyWithImpl(
      _$OrderRateDataImpl _value, $Res Function(_$OrderRateDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderRateData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderRates = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$OrderRateDataImpl(
      orderRates: freezed == orderRates
          ? _value._orderRates
          : orderRates // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderRateDataImpl implements _OrderRateData {
  const _$OrderRateDataImpl({final List<int>? orderRates, this.userId})
      : _orderRates = orderRates;

  factory _$OrderRateDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderRateDataImplFromJson(json);

  final List<int>? _orderRates;
  @override
  List<int>? get orderRates {
    final value = _orderRates;
    if (value == null) return null;
    if (_orderRates is EqualUnmodifiableListView) return _orderRates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? userId;

  @override
  String toString() {
    return 'OrderRateData(orderRates: $orderRates, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderRateDataImpl &&
            const DeepCollectionEquality()
                .equals(other._orderRates, _orderRates) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_orderRates), userId);

  /// Create a copy of OrderRateData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderRateDataImplCopyWith<_$OrderRateDataImpl> get copyWith =>
      __$$OrderRateDataImplCopyWithImpl<_$OrderRateDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderRateDataImplToJson(
      this,
    );
  }
}

abstract class _OrderRateData implements OrderRateData {
  const factory _OrderRateData(
      {final List<int>? orderRates, final int? userId}) = _$OrderRateDataImpl;

  factory _OrderRateData.fromJson(Map<String, dynamic> json) =
      _$OrderRateDataImpl.fromJson;

  @override
  List<int>? get orderRates;
  @override
  int? get userId;

  /// Create a copy of OrderRateData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderRateDataImplCopyWith<_$OrderRateDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
