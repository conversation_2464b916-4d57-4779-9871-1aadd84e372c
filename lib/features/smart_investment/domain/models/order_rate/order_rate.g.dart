// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_rate.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderRateImpl _$$OrderRateImplFromJson(Map<String, dynamic> json) =>
    _$OrderRateImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : OrderRateData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$OrderRateImplToJson(_$OrderRateImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$OrderRateDataImpl _$$OrderRateDataImplFromJson(Map<String, dynamic> json) =>
    _$OrderRateDataImpl(
      orderRates: (json['orderRates'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$OrderRateDataImplToJson(_$OrderRateDataImpl instance) =>
    <String, dynamic>{
      'orderRates': instance.orderRates,
      'userId': instance.userId,
    };
