// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reject_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RejectResponseImpl _$$RejectResponseImplFromJson(Map<String, dynamic> json) =>
    _$RejectResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$RejectResponseImplToJson(
        _$RejectResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => ListElement.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$ListElementImpl _$$ListElementImplFromJson(Map<String, dynamic> json) =>
    _$ListElementImpl(
      auditContent: json['auditContent'] as String?,
      bindingStatus: (json['bindingStatus'] as num?)?.toInt(),
      commissionRate: json['commissionRate'],
      createTime: json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
      cycle: json['cycle'],
      id: (json['id'] as num?)?.toInt(),
      mentor: json['mentor'] as String?,
      orderAmount: (json['orderAmount'] as num?)?.toInt(),
      orderNo: json['orderNo'] as String?,
      productName: json['productName'] as String?,
      productNumber: json['productNumber'],
      productType: (json['productType'] as num?)?.toInt(),
      totalIncome: json['totalIncome'],
      unbindTimeEnd: json['unbindTimeEnd'],
      unbindTimeStar: json['unbindTimeStar'],
      unbindingDays: json['unbindingDays'],
      yesterdayIncome: json['yesterdayIncome'],
    );

Map<String, dynamic> _$$ListElementImplToJson(_$ListElementImpl instance) =>
    <String, dynamic>{
      'auditContent': instance.auditContent,
      'bindingStatus': instance.bindingStatus,
      'commissionRate': instance.commissionRate,
      'createTime': instance.createTime?.toIso8601String(),
      'cycle': instance.cycle,
      'id': instance.id,
      'mentor': instance.mentor,
      'orderAmount': instance.orderAmount,
      'orderNo': instance.orderNo,
      'productName': instance.productName,
      'productNumber': instance.productNumber,
      'productType': instance.productType,
      'totalIncome': instance.totalIncome,
      'unbindTimeEnd': instance.unbindTimeEnd,
      'unbindTimeStar': instance.unbindTimeStar,
      'unbindingDays': instance.unbindingDays,
      'yesterdayIncome': instance.yesterdayIncome,
    };
