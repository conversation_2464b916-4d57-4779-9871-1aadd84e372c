// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mentor_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MentorResponseImpl _$$MentorResponseImplFromJson(Map<String, dynamic> json) =>
    _$MentorResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : MentorData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$MentorResponseImplToJson(
        _$MentorResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$MentorDataImpl _$$MentorDataImplFromJson(Map<String, dynamic> json) =>
    _$MentorDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => Mentor.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$MentorDataImplToJson(_$MentorDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };

_$MentorImpl _$$MentorImplFromJson(Map<String, dynamic> json) => _$MentorImpl(
      avatar: json['avatar'] as String?,
      bio: json['bio'] as String?,
      company: json['company'] as String?,
      id: (json['id'] as num?)?.toInt(),
      maxDrawdown: (json['maxDrawdown'] as num?)?.toDouble(),
      mentorAllow: (json['mentorAllow'] as num?)?.toInt(),
      monthlyProfit: (json['monthlyProfit'] as num?)?.toDouble(),
      name: json['name'] as String?,
      nickname: json['nickname'] as String?,
      portfolio: json['portfolio'] as String?,
      position: json['position'] as String?,
      productAllowState: (json['productAllowState'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      winRate: (json['winRate'] as num?)?.toDouble(),
      yearsOfExperience: (json['yearsOfExperience'] as num?)?.toInt(),
      vipLevel: (json['vipLevel'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$MentorImplToJson(_$MentorImpl instance) =>
    <String, dynamic>{
      'avatar': instance.avatar,
      'bio': instance.bio,
      'company': instance.company,
      'id': instance.id,
      'maxDrawdown': instance.maxDrawdown,
      'mentorAllow': instance.mentorAllow,
      'monthlyProfit': instance.monthlyProfit,
      'name': instance.name,
      'nickname': instance.nickname,
      'portfolio': instance.portfolio,
      'position': instance.position,
      'productAllowState': instance.productAllowState,
      'status': instance.status,
      'winRate': instance.winRate,
      'yearsOfExperience': instance.yearsOfExperience,
      'vipLevel': instance.vipLevel,
    };
