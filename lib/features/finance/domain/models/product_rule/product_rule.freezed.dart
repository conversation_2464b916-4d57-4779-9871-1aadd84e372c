// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_rule.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductRule _$ProductRuleFromJson(Map<String, dynamic> json) {
  return _ProductRule.fromJson(json);
}

/// @nodoc
mixin _$ProductRule {
  int? get code => throw _privateConstructorUsedError;
  List<ProductRuleData>? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this ProductRule to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductRuleCopyWith<ProductRule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductRuleCopyWith<$Res> {
  factory $ProductRuleCopyWith(
          ProductRule value, $Res Function(ProductRule) then) =
      _$ProductRuleCopyWithImpl<$Res, ProductRule>;
  @useResult
  $Res call({int? code, List<ProductRuleData>? data, String? msg});
}

/// @nodoc
class _$ProductRuleCopyWithImpl<$Res, $Val extends ProductRule>
    implements $ProductRuleCopyWith<$Res> {
  _$ProductRuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductRuleData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductRuleImplCopyWith<$Res>
    implements $ProductRuleCopyWith<$Res> {
  factory _$$ProductRuleImplCopyWith(
          _$ProductRuleImpl value, $Res Function(_$ProductRuleImpl) then) =
      __$$ProductRuleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, List<ProductRuleData>? data, String? msg});
}

/// @nodoc
class __$$ProductRuleImplCopyWithImpl<$Res>
    extends _$ProductRuleCopyWithImpl<$Res, _$ProductRuleImpl>
    implements _$$ProductRuleImplCopyWith<$Res> {
  __$$ProductRuleImplCopyWithImpl(
      _$ProductRuleImpl _value, $Res Function(_$ProductRuleImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductRule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$ProductRuleImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductRuleData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductRuleImpl implements _ProductRule {
  const _$ProductRuleImpl(
      {this.code, final List<ProductRuleData>? data, this.msg})
      : _data = data;

  factory _$ProductRuleImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductRuleImplFromJson(json);

  @override
  final int? code;
  final List<ProductRuleData>? _data;
  @override
  List<ProductRuleData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? msg;

  @override
  String toString() {
    return 'ProductRule(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductRuleImpl &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, const DeepCollectionEquality().hash(_data), msg);

  /// Create a copy of ProductRule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductRuleImplCopyWith<_$ProductRuleImpl> get copyWith =>
      __$$ProductRuleImplCopyWithImpl<_$ProductRuleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductRuleImplToJson(
      this,
    );
  }
}

abstract class _ProductRule implements ProductRule {
  const factory _ProductRule(
      {final int? code,
      final List<ProductRuleData>? data,
      final String? msg}) = _$ProductRuleImpl;

  factory _ProductRule.fromJson(Map<String, dynamic> json) =
      _$ProductRuleImpl.fromJson;

  @override
  int? get code;
  @override
  List<ProductRuleData>? get data;
  @override
  String? get msg;

  /// Create a copy of ProductRule
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductRuleImplCopyWith<_$ProductRuleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductRuleData _$ProductRuleDataFromJson(Map<String, dynamic> json) {
  return _ProductRuleData.fromJson(json);
}

/// @nodoc
mixin _$ProductRuleData {
  int? get availableEndTime => throw _privateConstructorUsedError;
  int? get availableStartTime => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  int? get holdingDuration => throw _privateConstructorUsedError;
  int? get id => throw _privateConstructorUsedError;
  num? get interestRate => throw _privateConstructorUsedError;
  int? get minDay => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get sellTime => throw _privateConstructorUsedError;

  /// Serializes this ProductRuleData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductRuleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductRuleDataCopyWith<ProductRuleData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductRuleDataCopyWith<$Res> {
  factory $ProductRuleDataCopyWith(
          ProductRuleData value, $Res Function(ProductRuleData) then) =
      _$ProductRuleDataCopyWithImpl<$Res, ProductRuleData>;
  @useResult
  $Res call(
      {int? availableEndTime,
      int? availableStartTime,
      String? desc,
      int? holdingDuration,
      int? id,
      num? interestRate,
      int? minDay,
      String? name,
      int? sellTime});
}

/// @nodoc
class _$ProductRuleDataCopyWithImpl<$Res, $Val extends ProductRuleData>
    implements $ProductRuleDataCopyWith<$Res> {
  _$ProductRuleDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductRuleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableEndTime = freezed,
    Object? availableStartTime = freezed,
    Object? desc = freezed,
    Object? holdingDuration = freezed,
    Object? id = freezed,
    Object? interestRate = freezed,
    Object? minDay = freezed,
    Object? name = freezed,
    Object? sellTime = freezed,
  }) {
    return _then(_value.copyWith(
      availableEndTime: freezed == availableEndTime
          ? _value.availableEndTime
          : availableEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      availableStartTime: freezed == availableStartTime
          ? _value.availableStartTime
          : availableStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      holdingDuration: freezed == holdingDuration
          ? _value.holdingDuration
          : holdingDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as num?,
      minDay: freezed == minDay
          ? _value.minDay
          : minDay // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sellTime: freezed == sellTime
          ? _value.sellTime
          : sellTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductRuleDataImplCopyWith<$Res>
    implements $ProductRuleDataCopyWith<$Res> {
  factory _$$ProductRuleDataImplCopyWith(_$ProductRuleDataImpl value,
          $Res Function(_$ProductRuleDataImpl) then) =
      __$$ProductRuleDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? availableEndTime,
      int? availableStartTime,
      String? desc,
      int? holdingDuration,
      int? id,
      num? interestRate,
      int? minDay,
      String? name,
      int? sellTime});
}

/// @nodoc
class __$$ProductRuleDataImplCopyWithImpl<$Res>
    extends _$ProductRuleDataCopyWithImpl<$Res, _$ProductRuleDataImpl>
    implements _$$ProductRuleDataImplCopyWith<$Res> {
  __$$ProductRuleDataImplCopyWithImpl(
      _$ProductRuleDataImpl _value, $Res Function(_$ProductRuleDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductRuleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableEndTime = freezed,
    Object? availableStartTime = freezed,
    Object? desc = freezed,
    Object? holdingDuration = freezed,
    Object? id = freezed,
    Object? interestRate = freezed,
    Object? minDay = freezed,
    Object? name = freezed,
    Object? sellTime = freezed,
  }) {
    return _then(_$ProductRuleDataImpl(
      availableEndTime: freezed == availableEndTime
          ? _value.availableEndTime
          : availableEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      availableStartTime: freezed == availableStartTime
          ? _value.availableStartTime
          : availableStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      holdingDuration: freezed == holdingDuration
          ? _value.holdingDuration
          : holdingDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as num?,
      minDay: freezed == minDay
          ? _value.minDay
          : minDay // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sellTime: freezed == sellTime
          ? _value.sellTime
          : sellTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductRuleDataImpl implements _ProductRuleData {
  const _$ProductRuleDataImpl(
      {this.availableEndTime,
      this.availableStartTime,
      this.desc,
      this.holdingDuration,
      this.id,
      this.interestRate,
      this.minDay,
      this.name,
      this.sellTime});

  factory _$ProductRuleDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductRuleDataImplFromJson(json);

  @override
  final int? availableEndTime;
  @override
  final int? availableStartTime;
  @override
  final String? desc;
  @override
  final int? holdingDuration;
  @override
  final int? id;
  @override
  final num? interestRate;
  @override
  final int? minDay;
  @override
  final String? name;
  @override
  final int? sellTime;

  @override
  String toString() {
    return 'ProductRuleData(availableEndTime: $availableEndTime, availableStartTime: $availableStartTime, desc: $desc, holdingDuration: $holdingDuration, id: $id, interestRate: $interestRate, minDay: $minDay, name: $name, sellTime: $sellTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductRuleDataImpl &&
            (identical(other.availableEndTime, availableEndTime) ||
                other.availableEndTime == availableEndTime) &&
            (identical(other.availableStartTime, availableStartTime) ||
                other.availableStartTime == availableStartTime) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.holdingDuration, holdingDuration) ||
                other.holdingDuration == holdingDuration) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.minDay, minDay) || other.minDay == minDay) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.sellTime, sellTime) ||
                other.sellTime == sellTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      availableEndTime,
      availableStartTime,
      desc,
      holdingDuration,
      id,
      interestRate,
      minDay,
      name,
      sellTime);

  /// Create a copy of ProductRuleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductRuleDataImplCopyWith<_$ProductRuleDataImpl> get copyWith =>
      __$$ProductRuleDataImplCopyWithImpl<_$ProductRuleDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductRuleDataImplToJson(
      this,
    );
  }
}

abstract class _ProductRuleData implements ProductRuleData {
  const factory _ProductRuleData(
      {final int? availableEndTime,
      final int? availableStartTime,
      final String? desc,
      final int? holdingDuration,
      final int? id,
      final num? interestRate,
      final int? minDay,
      final String? name,
      final int? sellTime}) = _$ProductRuleDataImpl;

  factory _ProductRuleData.fromJson(Map<String, dynamic> json) =
      _$ProductRuleDataImpl.fromJson;

  @override
  int? get availableEndTime;
  @override
  int? get availableStartTime;
  @override
  String? get desc;
  @override
  int? get holdingDuration;
  @override
  int? get id;
  @override
  num? get interestRate;
  @override
  int? get minDay;
  @override
  String? get name;
  @override
  int? get sellTime;

  /// Create a copy of ProductRuleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductRuleDataImplCopyWith<_$ProductRuleDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
