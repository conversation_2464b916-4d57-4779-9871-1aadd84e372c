// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_rule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductRuleImpl _$$ProductRuleImplFromJson(Map<String, dynamic> json) =>
    _$ProductRuleImpl(
      code: (json['code'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => ProductRuleData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$ProductRuleImplToJson(_$ProductRuleImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ProductRuleDataImpl _$$ProductRuleDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductRuleDataImpl(
      availableEndTime: (json['availableEndTime'] as num?)?.toInt(),
      availableStartTime: (json['availableStartTime'] as num?)?.toInt(),
      desc: json['desc'] as String?,
      holdingDuration: (json['holdingDuration'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      interestRate: json['interestRate'] as num?,
      minDay: (json['minDay'] as num?)?.toInt(),
      name: json['name'] as String?,
      sellTime: (json['sellTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductRuleDataImplToJson(
        _$ProductRuleDataImpl instance) =>
    <String, dynamic>{
      'availableEndTime': instance.availableEndTime,
      'availableStartTime': instance.availableStartTime,
      'desc': instance.desc,
      'holdingDuration': instance.holdingDuration,
      'id': instance.id,
      'interestRate': instance.interestRate,
      'minDay': instance.minDay,
      'name': instance.name,
      'sellTime': instance.sellTime,
    };
