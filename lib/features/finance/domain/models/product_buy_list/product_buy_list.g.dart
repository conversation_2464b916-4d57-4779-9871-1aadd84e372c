// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_buy_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductBuyListImpl _$$ProductBuyListImplFromJson(Map<String, dynamic> json) =>
    _$ProductBuyListImpl(
      code: (json['code'] as num?)?.toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => ProductBuyListData.fromJson(e as Map<String, dynamic>))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$ProductBuyListImplToJson(
        _$ProductBuyListImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$ProductBuyListDataImpl _$$ProductBuyListDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductBuyListDataImpl(
      amount: json['amount'] as num?,
      createTime: json['createTime'] as String?,
      day: (json['day'] as num?)?.toInt(),
      description: json['description'] as String?,
      endTime: json['endTime'] as String?,
      financeProductName: json['financeProductName'] as String?,
      profitAmount: json['profitAmount'] as num?,
      rate: json['rate'] as num?,
      startTime: json['startTime'] as String?,
      status: (json['status'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductBuyListDataImplToJson(
        _$ProductBuyListDataImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'createTime': instance.createTime,
      'day': instance.day,
      'description': instance.description,
      'endTime': instance.endTime,
      'financeProductName': instance.financeProductName,
      'profitAmount': instance.profitAmount,
      'rate': instance.rate,
      'startTime': instance.startTime,
      'status': instance.status,
      'userId': instance.userId,
    };
