// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_buy_list.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductBuyList _$ProductBuyListFromJson(Map<String, dynamic> json) {
  return _ProductBuyList.fromJson(json);
}

/// @nodoc
mixin _$ProductBuyList {
  int? get code => throw _privateConstructorUsedError;
  List<ProductBuyListData>? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this ProductBuyList to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductBuyList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductBuyListCopyWith<ProductBuyList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductBuyListCopyWith<$Res> {
  factory $ProductBuyListCopyWith(
          ProductBuyList value, $Res Function(ProductBuyList) then) =
      _$ProductBuyListCopyWithImpl<$Res, ProductBuyList>;
  @useResult
  $Res call({int? code, List<ProductBuyListData>? data, String? msg});
}

/// @nodoc
class _$ProductBuyListCopyWithImpl<$Res, $Val extends ProductBuyList>
    implements $ProductBuyListCopyWith<$Res> {
  _$ProductBuyListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductBuyList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductBuyListData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductBuyListImplCopyWith<$Res>
    implements $ProductBuyListCopyWith<$Res> {
  factory _$$ProductBuyListImplCopyWith(_$ProductBuyListImpl value,
          $Res Function(_$ProductBuyListImpl) then) =
      __$$ProductBuyListImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, List<ProductBuyListData>? data, String? msg});
}

/// @nodoc
class __$$ProductBuyListImplCopyWithImpl<$Res>
    extends _$ProductBuyListCopyWithImpl<$Res, _$ProductBuyListImpl>
    implements _$$ProductBuyListImplCopyWith<$Res> {
  __$$ProductBuyListImplCopyWithImpl(
      _$ProductBuyListImpl _value, $Res Function(_$ProductBuyListImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductBuyList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$ProductBuyListImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ProductBuyListData>?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductBuyListImpl implements _ProductBuyList {
  const _$ProductBuyListImpl(
      {this.code, final List<ProductBuyListData>? data, this.msg})
      : _data = data;

  factory _$ProductBuyListImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductBuyListImplFromJson(json);

  @override
  final int? code;
  final List<ProductBuyListData>? _data;
  @override
  List<ProductBuyListData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? msg;

  @override
  String toString() {
    return 'ProductBuyList(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductBuyListImpl &&
            (identical(other.code, code) || other.code == code) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, code, const DeepCollectionEquality().hash(_data), msg);

  /// Create a copy of ProductBuyList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductBuyListImplCopyWith<_$ProductBuyListImpl> get copyWith =>
      __$$ProductBuyListImplCopyWithImpl<_$ProductBuyListImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductBuyListImplToJson(
      this,
    );
  }
}

abstract class _ProductBuyList implements ProductBuyList {
  const factory _ProductBuyList(
      {final int? code,
      final List<ProductBuyListData>? data,
      final String? msg}) = _$ProductBuyListImpl;

  factory _ProductBuyList.fromJson(Map<String, dynamic> json) =
      _$ProductBuyListImpl.fromJson;

  @override
  int? get code;
  @override
  List<ProductBuyListData>? get data;
  @override
  String? get msg;

  /// Create a copy of ProductBuyList
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductBuyListImplCopyWith<_$ProductBuyListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductBuyListData _$ProductBuyListDataFromJson(Map<String, dynamic> json) {
  return _ProductBuyListData.fromJson(json);
}

/// @nodoc
mixin _$ProductBuyListData {
  num? get amount => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get day => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get endTime => throw _privateConstructorUsedError;
  String? get financeProductName => throw _privateConstructorUsedError;
  num? get profitAmount => throw _privateConstructorUsedError;
  num? get rate => throw _privateConstructorUsedError;
  String? get startTime => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  int? get userId => throw _privateConstructorUsedError;

  /// Serializes this ProductBuyListData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductBuyListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductBuyListDataCopyWith<ProductBuyListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductBuyListDataCopyWith<$Res> {
  factory $ProductBuyListDataCopyWith(
          ProductBuyListData value, $Res Function(ProductBuyListData) then) =
      _$ProductBuyListDataCopyWithImpl<$Res, ProductBuyListData>;
  @useResult
  $Res call(
      {num? amount,
      String? createTime,
      int? day,
      String? description,
      String? endTime,
      String? financeProductName,
      num? profitAmount,
      num? rate,
      String? startTime,
      int? status,
      int? userId});
}

/// @nodoc
class _$ProductBuyListDataCopyWithImpl<$Res, $Val extends ProductBuyListData>
    implements $ProductBuyListDataCopyWith<$Res> {
  _$ProductBuyListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductBuyListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? createTime = freezed,
    Object? day = freezed,
    Object? description = freezed,
    Object? endTime = freezed,
    Object? financeProductName = freezed,
    Object? profitAmount = freezed,
    Object? rate = freezed,
    Object? startTime = freezed,
    Object? status = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as num?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      day: freezed == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      financeProductName: freezed == financeProductName
          ? _value.financeProductName
          : financeProductName // ignore: cast_nullable_to_non_nullable
              as String?,
      profitAmount: freezed == profitAmount
          ? _value.profitAmount
          : profitAmount // ignore: cast_nullable_to_non_nullable
              as num?,
      rate: freezed == rate
          ? _value.rate
          : rate // ignore: cast_nullable_to_non_nullable
              as num?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductBuyListDataImplCopyWith<$Res>
    implements $ProductBuyListDataCopyWith<$Res> {
  factory _$$ProductBuyListDataImplCopyWith(_$ProductBuyListDataImpl value,
          $Res Function(_$ProductBuyListDataImpl) then) =
      __$$ProductBuyListDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {num? amount,
      String? createTime,
      int? day,
      String? description,
      String? endTime,
      String? financeProductName,
      num? profitAmount,
      num? rate,
      String? startTime,
      int? status,
      int? userId});
}

/// @nodoc
class __$$ProductBuyListDataImplCopyWithImpl<$Res>
    extends _$ProductBuyListDataCopyWithImpl<$Res, _$ProductBuyListDataImpl>
    implements _$$ProductBuyListDataImplCopyWith<$Res> {
  __$$ProductBuyListDataImplCopyWithImpl(_$ProductBuyListDataImpl _value,
      $Res Function(_$ProductBuyListDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductBuyListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? createTime = freezed,
    Object? day = freezed,
    Object? description = freezed,
    Object? endTime = freezed,
    Object? financeProductName = freezed,
    Object? profitAmount = freezed,
    Object? rate = freezed,
    Object? startTime = freezed,
    Object? status = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$ProductBuyListDataImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as num?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      day: freezed == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      financeProductName: freezed == financeProductName
          ? _value.financeProductName
          : financeProductName // ignore: cast_nullable_to_non_nullable
              as String?,
      profitAmount: freezed == profitAmount
          ? _value.profitAmount
          : profitAmount // ignore: cast_nullable_to_non_nullable
              as num?,
      rate: freezed == rate
          ? _value.rate
          : rate // ignore: cast_nullable_to_non_nullable
              as num?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductBuyListDataImpl implements _ProductBuyListData {
  const _$ProductBuyListDataImpl(
      {this.amount,
      this.createTime,
      this.day,
      this.description,
      this.endTime,
      this.financeProductName,
      this.profitAmount,
      this.rate,
      this.startTime,
      this.status,
      this.userId});

  factory _$ProductBuyListDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductBuyListDataImplFromJson(json);

  @override
  final num? amount;
  @override
  final String? createTime;
  @override
  final int? day;
  @override
  final String? description;
  @override
  final String? endTime;
  @override
  final String? financeProductName;
  @override
  final num? profitAmount;
  @override
  final num? rate;
  @override
  final String? startTime;
  @override
  final int? status;
  @override
  final int? userId;

  @override
  String toString() {
    return 'ProductBuyListData(amount: $amount, createTime: $createTime, day: $day, description: $description, endTime: $endTime, financeProductName: $financeProductName, profitAmount: $profitAmount, rate: $rate, startTime: $startTime, status: $status, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductBuyListDataImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.day, day) || other.day == day) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.financeProductName, financeProductName) ||
                other.financeProductName == financeProductName) &&
            (identical(other.profitAmount, profitAmount) ||
                other.profitAmount == profitAmount) &&
            (identical(other.rate, rate) || other.rate == rate) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      amount,
      createTime,
      day,
      description,
      endTime,
      financeProductName,
      profitAmount,
      rate,
      startTime,
      status,
      userId);

  /// Create a copy of ProductBuyListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductBuyListDataImplCopyWith<_$ProductBuyListDataImpl> get copyWith =>
      __$$ProductBuyListDataImplCopyWithImpl<_$ProductBuyListDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductBuyListDataImplToJson(
      this,
    );
  }
}

abstract class _ProductBuyListData implements ProductBuyListData {
  const factory _ProductBuyListData(
      {final num? amount,
      final String? createTime,
      final int? day,
      final String? description,
      final String? endTime,
      final String? financeProductName,
      final num? profitAmount,
      final num? rate,
      final String? startTime,
      final int? status,
      final int? userId}) = _$ProductBuyListDataImpl;

  factory _ProductBuyListData.fromJson(Map<String, dynamic> json) =
      _$ProductBuyListDataImpl.fromJson;

  @override
  num? get amount;
  @override
  String? get createTime;
  @override
  int? get day;
  @override
  String? get description;
  @override
  String? get endTime;
  @override
  String? get financeProductName;
  @override
  num? get profitAmount;
  @override
  num? get rate;
  @override
  String? get startTime;
  @override
  int? get status;
  @override
  int? get userId;

  /// Create a copy of ProductBuyListData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductBuyListDataImplCopyWith<_$ProductBuyListDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
