// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileInfoImpl _$$ProfileInfoImplFromJson(Map<String, dynamic> json) =>
    _$ProfileInfoImpl(
      code: (json['code'] as num?)?.toInt(),
      infoData: json['data'] == null
          ? null
          : InfoData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$ProfileInfoImplToJson(_$ProfileInfoImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.infoData,
      'msg': instance.msg,
    };

_$InfoDataImpl _$$InfoDataImplFromJson(Map<String, dynamic> json) =>
    _$InfoDataImpl(
      expendAddresses: (json['expendAddresses'] as List<dynamic>?)
          ?.map((e) => ExpendAddress.fromJson(e as Map<String, dynamic>))
          .toList(),
      invitationCode: json['invitationCode'] as String?,
      userId: (json['userId'] as num?)?.toInt(),
      userLevel: (json['userLevel'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$InfoDataImplToJson(_$InfoDataImpl instance) =>
    <String, dynamic>{
      'expendAddresses': instance.expendAddresses,
      'invitationCode': instance.invitationCode,
      'userId': instance.userId,
      'userLevel': instance.userLevel,
    };

_$ExpendAddressImpl _$$ExpendAddressImplFromJson(Map<String, dynamic> json) =>
    _$ExpendAddressImpl(
      address: json['address'] as String?,
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$ExpendAddressImplToJson(_$ExpendAddressImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };
