// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dist_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DistResponseImpl _$$DistResponseImplFromJson(Map<String, dynamic> json) =>
    _$DistResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      msg: json['msg'] as String?,
      data: json['data'] == null
          ? null
          : DistData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DistResponseImplToJson(_$DistResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_$DistDataImpl _$$DistDataImplFromJson(Map<String, dynamic> json) =>
    _$DistDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      low: (json['low'] as num?)?.toInt(),
      up: (json['up'] as num?)?.toInt(),
      zero: (json['zero'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DistDataImplToJson(_$DistDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'low': instance.low,
      'up': instance.up,
      'zero': instance.zero,
    };
