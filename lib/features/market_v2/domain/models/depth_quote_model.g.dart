// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'depth_quote_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DepthQuoteModelImpl _$$DepthQuoteModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DepthQuoteModelImpl(
      code: (json['code'] as num?)?.toInt(),
      msg: json['msg'] as String?,
      data: json['data'] == null
          ? null
          : DepthQuoteData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DepthQuoteModelImplToJson(
        _$DepthQuoteModelImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_$DepthQuoteDataImpl _$$DepthQuoteDataImplFromJson(Map<String, dynamic> json) =>
    _$DepthQuoteDataImpl(
      bid: (json['bid'] as List<dynamic>?)
          ?.map((e) => Bid.fromJson(e as Map<String, dynamic>))
          .toList(),
      ask: (json['ask'] as List<dynamic>?)
          ?.map((e) => Ask.fromJson(e as Map<String, dynamic>))
          .toList(),
      symbol: json['symbol'] as String?,
      market: json['market'] as String?,
      securityType: json['securityType'] as String?,
      close: (json['close'] as num?)?.toDouble(),
      latestPrice: (json['latestPrice'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$DepthQuoteDataImplToJson(
        _$DepthQuoteDataImpl instance) =>
    <String, dynamic>{
      'bid': instance.bid,
      'ask': instance.ask,
      'symbol': instance.symbol,
      'market': instance.market,
      'securityType': instance.securityType,
      'close': instance.close,
      'latestPrice': instance.latestPrice,
    };

_$BidImpl _$$BidImplFromJson(Map<String, dynamic> json) => _$BidImpl(
      price: (json['price'] as num?)?.toDouble(),
      vol: (json['vol'] as num?)?.toInt(),
      depthNo: (json['depthNo'] as num?)?.toInt(),
      no: (json['no'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$BidImplToJson(_$BidImpl instance) => <String, dynamic>{
      'price': instance.price,
      'vol': instance.vol,
      'depthNo': instance.depthNo,
      'no': instance.no,
    };

_$AskImpl _$$AskImplFromJson(Map<String, dynamic> json) => _$AskImpl(
      price: (json['price'] as num?)?.toDouble(),
      vol: (json['vol'] as num?)?.toInt(),
      depthNo: (json['depthNo'] as num?)?.toInt(),
      no: (json['no'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$AskImplToJson(_$AskImpl instance) => <String, dynamic>{
      'price': instance.price,
      'vol': instance.vol,
      'depthNo': instance.depthNo,
      'no': instance.no,
    };
