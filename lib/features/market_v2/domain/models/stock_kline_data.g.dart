// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_kline_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StockKlineResponseImpl _$$StockKlineResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$StockKlineResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : StockKlineData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$StockKlineResponseImplToJson(
        _$StockKlineResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$StockKlineDataImpl _$$StockKlineDataImplFromJson(Map<String, dynamic> json) =>
    _$StockKlineDataImpl(
      detail: json['detail'] == null
          ? null
          : StockItem.fromJson(json['detail'] as Map<String, dynamic>),
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => KlineItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StockKlineDataImplToJson(
        _$StockKlineDataImpl instance) =>
    <String, dynamic>{
      'detail': instance.detail,
      'list': instance.list,
    };

_$KlineItemImpl _$$KlineItemImplFromJson(Map<String, dynamic> json) =>
    _$KlineItemImpl(
      volume: (json['volume'] as num?)?.toDouble(),
      price: (json['price'] as num?)?.toDouble(),
      avgPrice: (json['avgPrice'] as num?)?.toDouble(),
      time: (json['time'] as num?)?.toInt(),
      open: (json['open'] as num?)?.toDouble(),
      close: (json['close'] as num?)?.toDouble(),
      high: (json['high'] as num?)?.toDouble(),
      low: (json['low'] as num?)?.toDouble(),
      apiEndTime: (json['apiEndTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$KlineItemImplToJson(_$KlineItemImpl instance) =>
    <String, dynamic>{
      'volume': instance.volume,
      'price': instance.price,
      'avgPrice': instance.avgPrice,
      'time': instance.time,
      'open': instance.open,
      'close': instance.close,
      'high': instance.high,
      'low': instance.low,
      'apiEndTime': instance.apiEndTime,
    };
