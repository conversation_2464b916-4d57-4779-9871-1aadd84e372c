// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component_stock_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ComponentStockResponseImpl _$$ComponentStockResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ComponentStockResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      msg: json['msg'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => ComponentStockData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ComponentStockResponseImplToJson(
        _$ComponentStockResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_$ComponentStockDataImpl _$$ComponentStockDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ComponentStockDataImpl(
      kline: (json['kline'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      latestPrice: (json['latestPrice'] as num?)?.toDouble(),
      priceChange: (json['priceChange'] as num?)?.toDouble(),
      priceChangePercent: (json['priceChangePercent'] as num?)?.toDouble(),
      symbol: json['symbol'] as String?,
    );

Map<String, dynamic> _$$ComponentStockDataImplToJson(
        _$ComponentStockDataImpl instance) =>
    <String, dynamic>{
      'kline': instance.kline,
      'latestPrice': instance.latestPrice,
      'priceChange': instance.priceChange,
      'priceChangePercent': instance.priceChangePercent,
      'symbol': instance.symbol,
    };
