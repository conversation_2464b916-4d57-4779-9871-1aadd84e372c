// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plate_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PlateResponseImpl _$$PlateResponseImplFromJson(Map<String, dynamic> json) =>
    _$PlateResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : PlateData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$PlateResponseImplToJson(_$PlateResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$PlateDataImpl _$$PlateDataImplFromJson(Map<String, dynamic> json) =>
    _$PlateDataImpl(
      list: _mapPlateItemToStockItem(json['list']),
    );

Map<String, dynamic> _$$PlateDataImplToJson(_$PlateDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
    };
