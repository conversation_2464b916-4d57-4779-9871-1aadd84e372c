// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_table_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StockTableResponseImpl _$$StockTableResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$StockTableResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : StockTableData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$StockTableResponseImplToJson(
        _$StockTableResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$StockTableDataImpl _$$StockTableDataImplFromJson(Map<String, dynamic> json) =>
    _$StockTableDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => StockItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: (json['pageNum'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$StockTableDataImplToJson(
        _$StockTableDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'total': instance.total,
    };
