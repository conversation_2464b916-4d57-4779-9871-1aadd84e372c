// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StockResponseImpl _$$StockResponseImplFromJson(Map<String, dynamic> json) =>
    _$StockResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      msg: json['msg'] as String?,
      data: json['data'] == null
          ? null
          : StockData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$StockResponseImplToJson(_$StockResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

_$StockDataImpl _$$StockDataImplFromJson(Map<String, dynamic> json) =>
    _$StockDataImpl(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => StockItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$StockDataImplToJson(_$StockDataImpl instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

_$StockItemImpl _$$StockItemImplFromJson(Map<String, dynamic> json) =>
    _$StockItemImpl(
      peStatic: _parseStringToDouble(json['peStatic']),
      high52w: _parseStringToDouble(json['high52w']),
      peLyr: _parseStringToDouble(json['peLyr']),
      precision: _parseStringToInt(json['precision']),
      securityStatus: _parseStringToInt(json['securityStatus']),
      gain: _parseStringToDouble(json['gain']),
      high: _parseStringToDouble(json['high']),
      low: _parseStringToDouble(json['low']),
      close: _parseStringToDouble(json['close']),
      turnover: _parseStringToDouble(json['turnover']),
      latestPrice: _parseStringToDouble(json['latestPrice']),
      amount: _parseStringToDouble(json['amount']),
      chg: _parseStringToDouble(json['chg']),
      lotSize: _parseStringToInt(json['lotSize']),
      marketValue: _parseStringToDouble(json['marketValue']),
      dividendRate: _parseStringToDouble(json['dividendRate']),
      priceUpLimited: _parseStringToDouble(json['priceUpLimited']),
      priceDownLimited: _parseStringToDouble(json['priceDownLimited']),
      volume: _parseStringToDouble(json['volume']),
      pb: _parseStringToDouble(json['pb']),
      leadupGain: _parseStringToDouble(json['leadupGain']),
      symbol: json['symbol'] as String?,
      industryPlate: json['industryPlate'] as String?,
      currency: json['currency'] as String?,
      market: json['market'] as String?,
      securityType: json['securityType'] as String?,
      name: json['name'] as String?,
      latestTime: _parseStringToInt(json['latestTime']),
      open: _parseStringToDouble(json['open']),
    );

Map<String, dynamic> _$$StockItemImplToJson(_$StockItemImpl instance) =>
    <String, dynamic>{
      'peStatic': instance.peStatic,
      'high52w': instance.high52w,
      'peLyr': instance.peLyr,
      'precision': instance.precision,
      'securityStatus': instance.securityStatus,
      'gain': instance.gain,
      'high': instance.high,
      'low': instance.low,
      'close': instance.close,
      'turnover': instance.turnover,
      'latestPrice': instance.latestPrice,
      'amount': instance.amount,
      'chg': instance.chg,
      'lotSize': instance.lotSize,
      'marketValue': instance.marketValue,
      'dividendRate': instance.dividendRate,
      'priceUpLimited': instance.priceUpLimited,
      'priceDownLimited': instance.priceDownLimited,
      'volume': instance.volume,
      'pb': instance.pb,
      'leadupGain': instance.leadupGain,
      'symbol': instance.symbol,
      'industryPlate': instance.industryPlate,
      'currency': instance.currency,
      'market': instance.market,
      'securityType': instance.securityType,
      'name': instance.name,
      'latestTime': instance.latestTime,
      'open': instance.open,
    };
