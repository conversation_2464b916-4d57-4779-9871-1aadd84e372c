#!/bin/bash

# test_multi_flavor_fix.sh - Test the multi-flavor asset fix
# Usage: ./scripts/test_multi_flavor_fix.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Unicode symbols
CHECKMARK="✅"
CROSS="❌"
GEAR="⚙️"
PACKAGE="📦"

# Function to print colored output
print_status() {
    local color=$1
    local symbol=$2
    local message=$3
    echo -e "${color}${symbol} ${message}${NC}"
}

print_header() {
    echo ""
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo ""
}

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Change to project root directory
cd "$PROJECT_ROOT" || {
    print_status $RED $CROSS "Error: Could not change to project root directory"
    exit 1
}

print_header "Multi-Flavor Asset Fix Test"

# Test flavors
TEST_FLAVORS=("sf_app" "cfroex")
TOTAL_FLAVORS=${#TEST_FLAVORS[@]}

# Backup original pubspec.yaml for safety
if [[ -f "pubspec.yaml" ]]; then
    cp "pubspec.yaml" "pubspec.yaml.test_backup"
    print_status $GREEN $CHECKMARK "Created test backup of pubspec.yaml"
else
    print_status $RED $CROSS "pubspec.yaml not found"
    exit 1
fi

# Global variables
ORIGINAL_PUBSPEC_BACKUP="pubspec.yaml.multi_flavor_backup"

# Function to create initial backup of pubspec.yaml
create_pubspec_backup() {
    if [[ -f "pubspec.yaml" ]] && [[ ! -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        cp "pubspec.yaml" "$ORIGINAL_PUBSPEC_BACKUP"
        print_status $GREEN $CHECKMARK "Created backup of original pubspec.yaml"
    fi
}

# Function to restore pubspec.yaml from backup for next flavor
restore_pubspec_for_next_flavor() {
    if [[ -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        cp "$ORIGINAL_PUBSPEC_BACKUP" "pubspec.yaml"
        print_status $GREEN $CHECKMARK "Restored clean pubspec.yaml for next flavor"
        # Remove any flavor marker file
        if [[ -f ".current_flavor" ]]; then
            rm ".current_flavor"
        fi
    else
        print_status $YELLOW "⚠️" "No backup found to restore from"
    fi
}

# Function to cleanup
cleanup_test() {
    # Restore original test backup
    if [[ -f "pubspec.yaml.test_backup" ]]; then
        cp "pubspec.yaml.test_backup" "pubspec.yaml"
        rm "pubspec.yaml.test_backup"
        print_status $GREEN $CHECKMARK "Restored original pubspec.yaml"
    fi
    
    # Clean up multi-flavor backup
    if [[ -f "$ORIGINAL_PUBSPEC_BACKUP" ]]; then
        rm "$ORIGINAL_PUBSPEC_BACKUP"
    fi
    
    # Remove any flavor marker file
    if [[ -f ".current_flavor" ]]; then
        rm ".current_flavor"
    fi
}

# Trap to ensure cleanup happens on script exit
trap cleanup_test EXIT

print_status $BLUE $PACKAGE "Testing multi-flavor asset optimization with proper reset..."

# Create initial backup
create_pubspec_backup

BUILD_COUNT=0

for FLAVOR in "${TEST_FLAVORS[@]}"; do
    BUILD_COUNT=$((BUILD_COUNT + 1))
    
    print_header "Testing $FLAVOR flavor ($BUILD_COUNT/$TOTAL_FLAVORS)"
    
    # Check if optimize script exists and run it
    if [[ -f "scripts/optimize_assets.sh" ]]; then
        print_status $YELLOW $GEAR "Optimizing assets for $FLAVOR..."
        if ./scripts/optimize_assets.sh $FLAVOR > /dev/null 2>&1; then
            print_status $GREEN $CHECKMARK "Assets optimized successfully"
        else
            print_status $RED $CROSS "Asset optimization failed for $FLAVOR"
            exit 1
        fi
    else
        print_status $RED $CROSS "Asset optimizer not found"
        exit 1
    fi
    
    # Check if the assets section contains the right flavor
    if grep -q "assets/logo/$FLAVOR/" pubspec.yaml; then
        print_status $GREEN $CHECKMARK "Correct logo assets found for $FLAVOR"
    else
        print_status $RED $CROSS "Logo assets missing for $FLAVOR"
        echo "Current assets section:"
        grep -A 20 "assets:" pubspec.yaml | head -20
        exit 1
    fi
    
    # Simulate build process
    print_status $BLUE $PACKAGE "Simulating build for $FLAVOR..."
    sleep 1
    
    # Restore for next flavor (except for the last flavor)
    if [[ $BUILD_COUNT -lt $TOTAL_FLAVORS ]]; then
        print_status $YELLOW $GEAR "Preparing for next flavor..."
        restore_pubspec_for_next_flavor
        
        # Verify restoration
        if [[ ! -f ".current_flavor" ]]; then
            print_status $GREEN $CHECKMARK "Flavor marker properly removed"
        else
            print_status $YELLOW "⚠️" "Flavor marker still exists"
        fi
    fi
    
    echo ""
done

print_header "Test Results"
print_status $GREEN $CHECKMARK "Multi-flavor asset fix test completed successfully!"
echo ""
echo "✅ Each flavor got properly optimized assets"
echo "✅ Assets were properly reset between flavors"
echo "✅ No cross-contamination between flavors"
echo ""
echo "The fix is working correctly!"
