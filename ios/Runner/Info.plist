<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(BUNDLE_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>ar-SA</string>
		<string>cs-CZ</string>
		<string>da-DK</string>
		<string>de-DE</string>
		<string>el-GR</string>
		<string>en-US</string>
		<string>es-ES</string>
		<string>es-MX</string>
		<string>et-EE</string>
		<string>fi-FI</string>
		<string>fr-FR</string>
		<string>hi-IN</string>
		<string>hu-HU</string>
		<string>it-IT</string>
		<string>ja-JP</string>
		<string>ko-KR</string>
		<string>lt-LT</string>
		<string>lv-LV</string>
		<string>ms-MY</string>
		<string>mt-MT</string>
		<string>nl-NL</string>
		<string>no-NO</string>
		<string>pl-PL</string>
		<string>pt-BR</string>
		<string>ru-RU</string>
		<string>sk-SK</string>
		<string>sl-SI</string>
		<string>sv-SE</string>
		<string>tr-TR</string>
		<string>uk-UA</string>
		<string>zh-CN</string>
		<string>zh-HK</string>
	</array>
	<key>CFBundleName</key>
	<string>$(BUNDLE_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to your camera to capture documents and images</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires access to microphone to record videos and audios</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to your photos to upload images and documents</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
