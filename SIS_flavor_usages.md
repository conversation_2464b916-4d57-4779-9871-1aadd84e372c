# SIS Flavor Usages in Codebase

This document lists all locations where the SIS flavor (`isSisFlavor`, `Flavor.sis`, or `shouldFetchWalletCoinsFromApi`) is used for conditional logic or configuration. The SIS flavor represents a specialized investment platform variant with distinct API endpoints, payment systems, and UI features compared to other flavors (sf_app, cfroex, ncm).

---

## Core/Config/Utils
- **lib/core/utils/utils.dart**
  - Defines `isSisFlavor` getter: Primary runtime check used throughout the app for SIS-specific conditional logic and feature toggles.
  - Contains commented market color logic that could be flavor-specific (currently disabled).
- **lib/flavors.dart**
  - Defines `Flavor.sis` enum value with dedicated configuration:
    - **Base URL**: `https://api.sisinvestors.com` (production) / `https://sis-api.superfuture.world` (debug)
    - **WebSocket URL**: `wss://api.sisinvestors.com/ws` (production) / `wss://sis-api.superfuture.world/ws` (debug)
    - **App URL**: `https://sisinvestors.com/`
    - **Title**: "SIS Investors"
- **lib/core/config/payment_config.dart**
  - `isSisFlavor`: Determines payment type source - static enum values (non-SIS) vs dynamic API data (SIS only).
  - `shouldFetchWalletCoinsFromApi`: Gateway flag that enables wallet coin API calls exclusively for SIS flavor.
  - Provides fallback to static payment types (ERC20, TRC20) when SIS API data is unavailable.
- **lib/core/config/app_config.dart**
  - Acts as main configuration controller that delegates payment and wallet coin logic to `PaymentConfig` while maintaining centralized access.

## Wallet/Deposit & Withdrawal
- **lib/features/wallet/deposit/logic/deposit_cubit.dart**
  - Uses `AppConfig.shouldFetchWalletCoinsFromApi` to conditionally fetch wallet coins from API (SIS only) vs using static payment types.
  - Implements different initialization flows: API-based for SIS, static enum-based for other flavors.
- **lib/features/wallet/deposit/domain/services/deposit_service.dart**
  - Fetches dynamic wallet coin data from API exclusively for SIS flavor to populate available payment options.
- **lib/features/wallet/deposit/screens/deposit_screen.dart**
  - **Initialization**: SIS fetches wallet coins first, then auto-sets default payment type; non-SIS directly initializes with static payment types.
  - **Refresh Logic**: SIS refreshes wallet coins from API; non-SIS refreshes with current/default static payment type.
  - Multiple UI conditional checks based on `AppConfig.shouldFetchWalletCoinsFromApi` for payment type selection and address generation.
- **lib/features/profile/screens/add_withdrawal_address_screen.dart**
  - Uses `AppConfig.shouldFetchWalletCoinsFromApi` to determine withdrawal address validation and coin selection logic for SIS vs static types.

## Wallet/Records & UI
- **lib/features/wallet/records/screens/records_screen.dart**
  - `isSisFlavor`: Restricts SIS users to funding wallet records only (excludes community/trading wallet records).
  - Pagination and refresh logic conditionally skip community records for SIS to prevent API calls for unavailable data.
- **lib/features/wallet/records/widgets/wallet_slider_records.dart**
  - `isSisFlavor`: Dynamically builds wallet items list excluding trading wallet for SIS (shows only funding wallet).
  - Conditional rendering of carousel items and dot indicators based on available wallet types.
  - Community wallet slider item (`isCommunity: index == 1 && !isSisFlavor`) only appears for non-SIS flavors.

## Home & Navigation
- **lib/features/home/<USER>/collection_drawers.dart**
  - `isSisFlavor`: Removes "Transfer" action button from home screen for SIS users (transfer functionality not available).
  - Adjusts shimmer loading animation count to match reduced action buttons for SIS.

## Profile & Settings
- **lib/features/profile/screens/profile_screen.dart**
  - `isSisFlavor`: Adds exclusive "Benefit Rules" menu item for SIS users (links to SIS-specific benefit documentation).
  - **Wallet Display**: Shows only funding wallet for SIS users (excludes trading/community wallet slider).
  - Maintains consistent profile structure while adapting available features based on flavor capabilities.

## Smart Investment & Trading
- **lib/features/smart_investment/domain/services/smart_investment_service.dart**
  - `isSisFlavor`: Modifies API payload structure and parameters for SIS-specific investment endpoints.
- **lib/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart**
  - **Account Type Logic**: Sets different account types and balance calculation methods for SIS vs other flavors.
  - Adapts investment flow and state management based on SIS platform requirements.
- **lib/features/smart_investment/screens/mentor_profile_screen.dart**
  - **UI Controls**: Conditionally disables/enables investment amount input fields and percentage buttons for SIS.
  - Implements different interaction patterns and validation rules based on SIS investment policies.
- **lib/features/smart_investment/widgets/purchase_bottom_sheet.dart**
  - **Wallet Integration**: Shows different wallet types and balance sources for SIS (funding-only) vs multi-wallet flavors.
  - Adapts purchase flow and confirmation logic for SIS-specific requirements.

## Transfer & Transactions
- **lib/features/transfer/widgets/transfer_screen_widgets/transfer_remaining_wallet/transfer_remaining_body.dart**
  - **Preview Functionality**: Transfer preview and calculation logic only available for non-SIS flavors.
  - SIS users have simplified transfer flow without advanced preview features.

## Finance & Products
- **lib/features/finance/screens/finance_screen.dart**
  - **Product Cards**: Displays different financial product information and UI layout for SIS flavor.
  - Adapts product offerings and presentation to match SIS investment platform focus.

---

## Summary of SIS Flavor Purpose

The SIS flavor transforms the app into a specialized investment platform with the following key characteristics:

### **Core Differences:**
- **API Infrastructure**: Dedicated SIS API endpoints (`api.sisinvestors.com`) with different data structures and business logic
- **Payment System**: Dynamic payment types fetched from API vs static ERC20/TRC20 enum for other flavors
- **Wallet Architecture**: Single funding wallet only (no trading/community wallets)
- **Investment Focus**: Specialized smart investment features with SIS-specific rules and UI adaptations

### **Technical Implementation:**
- **`isSisFlavor`**: Primary runtime boolean check used throughout the app for conditional logic
- **`shouldFetchWalletCoinsFromApi`**: Gateway flag that enables dynamic payment type fetching exclusively for SIS
- **`F.appFlavor == Flavor.sis`**: Direct enum comparison used sparingly in specific UI components
- **Graceful Fallbacks**: SIS can fall back to static payment types when API data is unavailable

### **Business Logic:**
- **Simplified UX**: Removes complex features (transfers, multiple wallets) for focused investment experience
- **Regulatory Compliance**: SIS-specific "Benefit Rules" and different investment validation rules
- **Platform Branding**: Complete visual and functional transformation to match SIS Investors brand identity

This flavor system allows the same codebase to serve multiple distinct investment platforms while maintaining code reusability and consistent architecture patterns.